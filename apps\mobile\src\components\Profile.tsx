import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  Alert,
  ScrollView,
  Platform,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useAppDispatch, useAppSelector } from '../hooks/redux';
import { logout, selectAuthUser } from '../redux/slices/authSlice';
import {
  requestNotificationPermission,
  selectFCMPermission,
  selectFCMInitialized,
  selectFCMRegistered,
  selectFCMLoading
} from '../redux/slices/fcmSlice';
import { ProfileNavigationProp, ProfileRouteProp } from '../navigation/types';
import { shadows, radius, spacing, typography, useTheme } from '../theme';
import LogoutModal from './LogoutModal';
import ClearChatModal from './ClearChatModal';
import RecallLimitModal from './RecallLimit';

interface ProfileProps {
  navigation: ProfileNavigationProp;
  route: ProfileRouteProp;
}

const Profile: React.FC<ProfileProps> = ({ navigation, route }) => {
  const dispatch = useAppDispatch();
  const currentUser = useAppSelector(selectAuthUser);
  const { username } = route.params;
  const { colors, isDark, toggleTheme } = useTheme();

  const fcmPermission = useAppSelector(selectFCMPermission);
  const fcmInitialized = useAppSelector(selectFCMInitialized);
  const fcmRegistered = useAppSelector(selectFCMRegistered);
  const fcmLoading = useAppSelector(selectFCMLoading);

  const [showLogoutModal, setShowLogoutModal] = useState(false);
  const [showClearDataModal, setShowClearDataModal] = useState(false);
  const [clearingData, setClearingData] = useState(false);

  const [isRecallModalVisible, setRecallModalVisible] = useState(false);
  const [recallLimit, setRecallLimit] = useState<number>(5);
  const [isUpdating, setIsUpdating] = useState(false);

  const handleBack = () => navigation.goBack();
  const handleLogout = () => setShowLogoutModal(true);
  const confirmLogout = () => {
    dispatch(logout());
    setShowLogoutModal(false);
  };

  const handleClearData = () => setShowClearDataModal(true);
  const handleRecallLimit = () => setRecallModalVisible(true);

  const confirmClearData = async () => {
    try {
      setClearingData(true);
      const { chatDBService } = await import('../database/service');
      await chatDBService.clearAllData();
      Alert.alert('Success', 'All data has been cleared');
      setShowClearDataModal(false);
    } catch (error) {
      console.error('Error clearing data:', error);
      Alert.alert('Error', 'Failed to clear data');
    } finally {
      setClearingData(false);
    }
  };

  const handleNotificationPermission = async () => {
    if (fcmLoading) return;

    try {
      await (dispatch(requestNotificationPermission() as any) as any).unwrap();
      Alert.alert('Success', 'Notification permission updated');
    } catch (error) {
      Alert.alert('Error', 'Failed to update notification permission');
    }
  };

  const getAvatarText = () => username?.charAt(0).toUpperCase() || '?';
  const isOwnProfile = currentUser === username;
  const styles = createStyles(colors);

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={handleBack}>
          <Icon name="chevron-left" size={28} color={colors.primary} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Profile</Text>
        <View style={styles.headerSpacer} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* User Info */}
        {isOwnProfile ? (
          <View style={styles.profileBanner}>
            <View style={styles.userSection}>
              <View style={styles.userAvatar}>
                <Text style={styles.avatarText}>{getAvatarText()}</Text>
              </View>
              <Text style={styles.username}>{username}</Text>
            </View>
          </View>
        ) : (
          <View style={styles.profileBanner}>
            <View style={styles.userSection}>
              <View style={styles.userAvatarOther}>
                <Text style={styles.avatarTextOther}>{getAvatarText()}</Text>
              </View>
              <Text style={styles.username}>{username}</Text>
            </View>
          </View>
        )}

        {/* Settings Sections - Only for own profile */}
        {isOwnProfile && (
          <>
            {/* Privacy Section */}
            <View style={styles.settingGroup}>
              <View style={styles.sectionHeaderRow}>
                <Icon name="lock" size={18} color={colors.danger} style={styles.sectionIcon} />
                <Text style={styles.groupTitle}>Privacy</Text>
              </View>
              <View style={styles.settingControlGroup}>
                <TouchableOpacity style={styles.settingItem} onPress={handleRecallLimit}>
                  <Text style={styles.settingLabel}>Recall Limit</Text>
                  <Text style={styles.settingValue}>{recallLimit === 0 ? 'Off' : recallLimit}</Text>
                </TouchableOpacity>
                <TouchableOpacity style={styles.settingItem} onPress={handleClearData}>
                  <Text style={styles.settingLabel}>Clear Data</Text>
                </TouchableOpacity>
                <TouchableOpacity style={styles.settingItem} onPress={handleClearData}>
                  <Text style={styles.settingLabel}>Disappearing Messages</Text>
                  <Text style={styles.settingValue}>24h</Text>
                </TouchableOpacity>
              </View>
            </View>

            {/* Appearance */}
            <View style={styles.settingGroup}>
              <View style={styles.sectionHeaderRow}>
                <Icon name="palette" size={18} color={colors.primary} style={styles.sectionIcon} />
                <Text style={styles.groupTitle}>Appearance</Text>
              </View>
              <View style={styles.settingControlGroup}>
                <TouchableOpacity style={styles.settingItem} onPress={toggleTheme}>
                  <Text style={styles.settingLabel}>Dark Mode</Text>
                  <Text style={styles.settingValue}>{isDark ? 'On' : 'Off'}</Text>
                </TouchableOpacity>
              </View>
            </View>

            {/* Notifications */}
            <View style={styles.settingGroup}>
              <View style={styles.sectionHeaderRow}>
                <Icon name="notifications" size={18} color={colors.primary} style={styles.sectionIcon} />
                <Text style={styles.groupTitle}>Notifications</Text>
              </View>
              <View style={styles.settingControlGroup}>
                <TouchableOpacity style={styles.settingItem} onPress={handleNotificationPermission} disabled={fcmLoading}>
                  <Text style={styles.settingLabel}>Push Notifications</Text>
                  <Text style={[styles.settingValue, !fcmPermission && styles.settingValueDisabled]}>
                    {fcmLoading ? 'Loading...' : fcmPermission ? 'Enabled' : 'Disabled'}
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity style={styles.settingItem}>
                  <Text style={styles.settingLabel}>FCM Status</Text>
                  <Text style={[styles.settingValue, !fcmInitialized && styles.settingValueDisabled]}>
                    {fcmInitialized ? (fcmRegistered ? 'Connected' : 'Ready') : 'Not Ready'}
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity style={styles.settingItem}>
                  <Text style={styles.settingLabel}>Emoji Reactions</Text>
                  <Text style={styles.settingValue}>On</Text>
                </TouchableOpacity>
              </View>
            </View>

            {/* Safety & Support */}
            <View style={styles.settingGroup}>
              <View style={styles.sectionHeaderRow}>
                <Icon name="support-agent" size={18} color={colors.warning} style={styles.sectionIcon} />
                <Text style={styles.groupTitle}>Safety & Support</Text>
              </View>
              <View style={styles.settingControlGroup}>
                <TouchableOpacity style={styles.settingItem}>
                  <Text style={styles.settingLabel}>Block</Text>
                </TouchableOpacity>
                <TouchableOpacity style={styles.settingItem}>
                  <Text style={styles.settingLabel}>Report</Text>
                </TouchableOpacity>
                <TouchableOpacity style={[styles.settingItem, styles.dangerItem]}>
                  <Text style={[styles.settingLabel, styles.dangerText]}>Disconnect User</Text>
                  <Icon name="delete-forever" size={20} color={colors.danger} />
                </TouchableOpacity>
              </View>
            </View>

            {/* Logout */}
            <View style={styles.logoutSection}>
              <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
                <Text style={styles.logoutButtonText}>Logout</Text>
              </TouchableOpacity>
            </View>
          </>
        )}

        {/* Other user profile */}
        {!isOwnProfile && (
          <>
            <View style={styles.settingGroup}>
              <View style={styles.sectionHeaderRow}>
                <Icon name="gavel" size={18} color={colors.warning} style={styles.sectionIcon} />
                <Text style={styles.groupTitle}>Actions</Text>
              </View>
              <View style={styles.settingControlGroupOther}>
                <TouchableOpacity style={styles.settingItemOther}>
                  <Text style={styles.settingLabelOther}>Block User</Text>
                </TouchableOpacity>
                <TouchableOpacity style={styles.settingItemOther}>
                  <Text style={styles.settingLabelOther}>Report User</Text>
                </TouchableOpacity>
              </View>
            </View>

            <View style={styles.settingGroup}>
              <View style={styles.sectionHeaderRow}>
                <Icon name="lock" size={18} color={colors.danger} style={styles.sectionIcon} />
                <Text style={styles.groupTitle}>Privacy</Text>
              </View>
              <View style={styles.settingControlGroupOther}>
                <TouchableOpacity style={styles.settingItemOther} onPress={handleRecallLimit}>
                  <Text style={styles.settingLabelOther}>Recall Limit</Text>
                  <Text style={styles.settingValueOther}>{recallLimit === 0 ? 'Off' : recallLimit}</Text>
                </TouchableOpacity>
                <TouchableOpacity style={styles.settingItemOther} onPress={handleClearData}>
                  <Text style={styles.settingLabelOther}>Clear Data</Text>
                </TouchableOpacity>
                <TouchableOpacity style={styles.settingItemOther} onPress={handleClearData}>
                  <Text style={styles.settingLabelOther}>Disappearing Messages</Text>
                  <Text style={styles.settingValueOther}>24h</Text>
                </TouchableOpacity>
              </View>
            </View>
          </>
        )}
      </ScrollView>

      {/* Modals */}
      <LogoutModal isVisible={showLogoutModal} onClose={() => setShowLogoutModal(false)} onConfirm={confirmLogout} />
      <ClearChatModal isVisible={showClearDataModal} onClose={() => setShowClearDataModal(false)} onConfirm={confirmClearData} loading={clearingData} />
      <RecallLimitModal isVisible={isRecallModalVisible} selectedLimit={recallLimit} onClose={() => setRecallModalVisible(false)} onConfirm={(newLimit) => {
        setRecallLimit(newLimit);
        setRecallModalVisible(false);
      }} loading={isUpdating} />
    </SafeAreaView>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.cardBackground,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.cardBackground,
    paddingHorizontal: spacing.md,
    paddingTop: Platform.OS === 'ios' ? spacing.sm : spacing.lg,
  },
  backButton: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerTitle: {
    ...typography.h2,
    fontSize: 18,
    fontFamily: 'Outfit-Medium',
    color: colors.text,
    flex: 1,
    textAlign: 'center',
  },
  headerSpacer: {
    width: 40,
  },
  content: {
    flex: 1,
  },
  userSection: {
    alignItems: 'center',
    paddingVertical: spacing.xl,
    marginBottom: spacing.md,
  },
  userAvatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: colors.primaryLight3,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  avatarText: {
    color: colors.primaryDark1,
    fontSize: 32,
    fontFamily: 'Outfit-Bold',
  },
  userAvatarOther: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: colors.toneLight3,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  avatarTextOther: {
    color: colors.toneDark1,
    fontSize: 32,
    fontFamily: 'Outfit-Bold',
  },
  username: {
    ...typography.h1,
    fontSize: 24,
    fontFamily: 'Outfit-Bold',
    color: colors.toneDark2,
  },
  settingGroup: {
    marginBottom: spacing.md,
  },
  settingControlGroup: {
    marginHorizontal: spacing.lg + 7,
    borderRadius: radius.md,
  },
  settingControlGroupOther: {
    marginHorizontal: spacing.lg,
    borderRadius: radius.md,
  },
  profileBanner: {
    marginHorizontal: spacing.lg,
    borderRadius: radius.lg,
    borderWidth: 1,
    borderColor: colors.toneLight2,
    marginVertical: spacing.lg,
  },
  sectionHeaderRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.sm,
    paddingBottom: spacing.xs,
  },
  sectionIcon: {
    marginRight: 5,
    color: colors.toneDark1,
  },
  groupTitle: {
    fontSize: 12,
    fontFamily: 'Outfit-Medium',
    color: colors.toneDark1,
    letterSpacing: 0.5,
    textTransform: 'uppercase',
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.md,
  },
  settingItemOther: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.md,
  },
  settingLabel: {
    ...typography.body,
    color: colors.primaryDark3,
    flex: 1,
  },
  settingLabelOther: {
    ...typography.body,
    color: colors.toneDark3,
    flex: 1,
  },
  settingValue: {
    ...typography.body,
    color: colors.primary,
    fontFamily: 'Outfit-Medium',
  },
  settingValueOther: {
    ...typography.body,
    color: colors.toneLight1,
    fontFamily: 'Outfit-Medium',
  },
  settingValueDisabled: {
    color: colors.textSecondary,
  },
  dangerItem: {
    borderBottomWidth: 0,
  },
  dangerText: {
    color: colors.danger,
  },
  logoutSection: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.xl,
  },
  logoutButton: {
    paddingVertical: spacing.md,
    borderRadius: radius.round,
    alignItems: 'center',
    backgroundColor: colors.toneLight3,
  },
  logoutButtonText: {
    color: colors.toneDark2,
    fontSize: 14,
    fontFamily: 'Outfit-Medium',
  },
});

export default Profile;
