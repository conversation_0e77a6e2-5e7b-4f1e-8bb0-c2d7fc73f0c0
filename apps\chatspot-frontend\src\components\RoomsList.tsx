import { useSelector } from 'react-redux';
import { selectUser } from '../redux/slices/authSlice';
import './RoomsList.css';
import { RootState } from '../redux/store';
import UserInfo from './UserInfo';
import EnhancedRoomItem from './EnhancedRoomItem';
// Import formatDistanceToNow function
const formatDistanceToNow = (date: Date, options?: { addSuffix?: boolean }): string => {
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) {
    return options?.addSuffix ? 'just now' : 'just now';
  }

  const diffInMinutes = Math.floor(diffInSeconds / 60);
  if (diffInMinutes < 60) {
    return options?.addSuffix ? `${diffInMinutes} m${diffInMinutes !== 1 ? 's' : ''} ago` : `${diffInMinutes} minute${diffInMinutes !== 1 ? 's' : ''}`;
  }

  const diffInHours = Math.floor(diffInMinutes / 60);
  if (diffInHours < 24) {
    return options?.addSuffix ? `${diffInHours} h${diffInHours !== 1 ? 's' : ''} ago` : `${diffInHours} hour${diffInHours !== 1 ? 's' : ''}`;
  }

  const diffInDays = Math.floor(diffInHours / 24);
  if (diffInDays < 7) {
    return options?.addSuffix ? `${diffInDays} d${diffInDays !== 1 ? 's' : ''} ago` : `${diffInDays} day${diffInDays !== 1 ? 's' : ''}`;
  }

  const diffInWeeks = Math.floor(diffInDays / 7);
  if (diffInWeeks < 4) {
    return options?.addSuffix ? `${diffInWeeks} w${diffInWeeks !== 1 ? 's' : ''} ago` : `${diffInWeeks} week${diffInWeeks !== 1 ? 's' : ''}`;
  }

  const diffInMonths = Math.floor(diffInDays / 30);
  if (diffInMonths < 12) {
    return options?.addSuffix ? `${diffInMonths} mths${diffInMonths !== 1 ? 's' : ''} ago` : `${diffInMonths} month${diffInMonths !== 1 ? 's' : ''}`;
  }

  const diffInYears = Math.floor(diffInDays / 365);
  return options?.addSuffix ? `${diffInYears} yrs${diffInYears !== 1 ? 's' : ''} ago` : `${diffInYears} year${diffInYears !== 1 ? 's' : ''}`;
};

interface Room {
  id: string;
  room_id: string;
  username: string;
  last_msg: string;
  updated: number;
}

type RoomRecord = Record<string, any> | Room;

interface RoomsListProps {
  rooms: RoomRecord[];
  onRoomSelect: (username: string) => void;
  selectedUsername: string | null;
  searchQuery?: string;
}

const RoomsList: React.FC<RoomsListProps> = ({
  rooms = [],
  onRoomSelect,
  selectedUsername = null,
  searchQuery = ''
}) => {
  // Filter rooms based on search query
  const filteredRooms = searchQuery.trim()
    ? rooms.filter(room =>
        room.username.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (room.last_msg && room.last_msg.toLowerCase().includes(searchQuery.toLowerCase()))
      )
    : rooms;

  return (
    <div className="rooms-list">
      {rooms.length === 0 ? (
        <div className="no-rooms">
          <div className="no-rooms-icon">📱</div>
          <p>No conversations yet</p>
          <p className="no-rooms-hint">Start chatting with someone!</p>
        </div>
      ) : filteredRooms.length === 0 ? (
        <div className="no-rooms">
          <p>No results found</p>
          <p className="no-rooms-hint">Try a different search term</p>
        </div>
      ) : (
        <ul className="rooms">
          {filteredRooms.map((room) => (
            <EnhancedRoomItem
              key={room.id}
              room={room}
              onRoomSelect={onRoomSelect}
              selectedUsername={selectedUsername}
            />
          ))}
        </ul>
      )}
    </div>
  );
};

export default RoomsList;
