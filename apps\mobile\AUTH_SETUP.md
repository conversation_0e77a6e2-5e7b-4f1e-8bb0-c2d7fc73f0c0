# Mobile Auth Setup Documentation

This document describes the authentication setup implemented in the ChatSpot mobile app, which mirrors the frontend implementation using Redux and Axios.

## Architecture Overview

The mobile auth system follows the same patterns as the chatspot-frontend:

- **Redux Toolkit** for state management
- **Redux Saga** for async operations
- **Axios** for API calls
- **AsyncStorage** for token persistence (mobile equivalent of localStorage)
- **React Navigation** for navigation between auth and main screens

## File Structure

```
src/
├── redux/
│   ├── store.ts                 # Redux store configuration
│   ├── slices/
│   │   └── authSlice.ts         # Auth state management
│   └── sagas/
│       ├── index.ts             # Root saga
│       └── authSaga.ts          # Auth async operations
├── services/
│   └── api.ts                   # API service with axios
├── utils/
│   ├── env.ts                   # Environment configuration
│   ├── storage.ts               # AsyncStorage utilities
│   └── userDataUtils.ts         # User data management
├── components/
│   ├── Login.tsx                # Login screen
│   ├── Register.tsx             # Register screen
│   ├── MainScreen.tsx           # Main app screen
│   └── LoadingScreen.tsx        # Loading screen
├── navigation/
│   └── AuthNavigator.tsx        # Auth navigation setup
├── hooks/
│   └── redux.ts                 # Typed Redux hooks
└── __tests__/
    └── auth.test.ts             # Auth functionality tests
```

## Key Features

### 1. **Redux State Management**
- Centralized auth state with loading, error, and user data
- Actions for login, register, logout, and error handling
- Selectors for accessing auth state

### 2. **Redux Saga for Async Operations**
- Handles login and register API calls
- Manages token storage and retrieval
- Error handling and state updates

### 3. **Secure Token Storage**
- Uses AsyncStorage for token persistence
- Automatic token inclusion in API requests
- Secure storage utilities for auth data

### 4. **API Integration**
- Axios instance with request/response interceptors
- Automatic token injection for authenticated requests
- Error handling and debugging

### 5. **Navigation Flow**
- Conditional rendering based on auth state
- Smooth transitions between auth and main screens
- Loading states during initialization

## Usage

### 1. **Login Flow**
```typescript
// User enters credentials
await authStorage.setTempCredentials(username, password);
dispatch(loginRequest());

// Saga handles the API call and storage
// On success: user is automatically navigated to main screen
```

### 2. **Register Flow**
```typescript
// Similar to login but with validation
if (password !== confirmPassword) {
  // Show error
  return;
}

await authStorage.setTempCredentials(username, password);
dispatch(registerRequest());
```

### 3. **Logout Flow**
```typescript
dispatch(logout());
// Automatically clears storage and navigates to auth screens
```

### 4. **Auto-Login**
```typescript
// On app start, check for stored token
const token = await authStorage.getToken();
const username = await authStorage.getUsername();
dispatch(initializeAuth({ token, username }));
```

## API Endpoints

The mobile app connects to the same backend as the frontend:

- `POST /auth/login` - User login
- `POST /auth/register` - User registration
- All other endpoints automatically include auth token

## Environment Configuration

```typescript
// Development
API_URL: 'http://localhost:3001'
WS_URL: 'ws://localhost:3001'

// Production
API_URL: 'https://chatspot-backend-8a7y.onrender.com'
WS_URL: 'wss://chatspot-backend-8a7y.onrender.com'
```

## Testing

Run the auth tests:
```bash
npm test -- --testPathPattern=auth.test.ts
```

Tests cover:
- Token storage and retrieval
- Username management
- Data clearing
- User data conflict detection

## Next Steps

1. **Socket.IO Integration** - Add real-time messaging
2. **Chat Screens** - Implement chat UI components
3. **Push Notifications** - Add mobile push notifications
4. **Offline Support** - Handle offline scenarios
5. **Biometric Auth** - Add fingerprint/face ID support

## Dependencies

Key packages installed:
- `@reduxjs/toolkit` - Redux state management
- `react-redux` - React Redux bindings
- `redux-saga` - Async operations
- `axios` - HTTP client
- `@react-native-async-storage/async-storage` - Storage
- `@react-navigation/native` - Navigation
- `@react-navigation/stack` - Stack navigator

## Differences from Frontend

1. **Storage**: AsyncStorage instead of localStorage
2. **Navigation**: React Navigation instead of React Router
3. **UI**: React Native components instead of HTML/CSS
4. **Environment**: React Native environment variables
5. **Platform**: Mobile-specific considerations
