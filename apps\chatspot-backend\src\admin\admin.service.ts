import { Injectable, NotFoundException, BadRequestException, ConflictException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../auth/user.entity';
import { UserDto } from '../auth/dto';
import * as bcrypt from 'bcrypt';

@Injectable()
export class AdminService {
  constructor(
    @InjectRepository(User)
    private userRepo: Repository<User>,
  ) {}

  /**
   * Get all users
   * @returns Array of all users
   */
  async getAllUsers(): Promise<UserDto[]> {
    const users = await this.userRepo.find();
    
    return users.map(user => ({
      id: user.id,
      username: user.username,
      isAdmin: user.isAdmin,
    }));
  }

  /**
   * Get user by ID
   * @param userId User ID to find
   * @returns User information
   */
  async getUserById(userId: string): Promise<UserDto> {
    const user = await this.userRepo.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException(`User with ID "${userId}" not found`);
    }

    return {
      id: user.id,
      username: user.username,
      isAdmin: user.isAdmin,
    };
  }

  /**
   * Create a new user
   * @param username Username for the new user
   * @param password Password for the new user
   * @param isAdmin Whether the user should be an admin
   * @returns The created user
   */
  async createUser(username: string, password: string, isAdmin: boolean = false): Promise<UserDto> {
    // Validate username format
    if (!/^[a-zA-Z0-9_]{3,20}$/.test(username)) {
      throw new BadRequestException('Invalid username format');
    }

    // Validate password strength
    if (!/^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/.test(password)) {
      throw new BadRequestException('Password too weak');
    }

    // Check if username already exists
    const existingUser = await this.userRepo.findOne({ where: { username } });
    if (existingUser) {
      throw new ConflictException('Username already exists');
    }

    // Hash password and create user
    const hashedPassword = await bcrypt.hash(password, 10);
    const newUser = this.userRepo.create({
      username,
      password: hashedPassword,
      isAdmin,
    });

    const savedUser = await this.userRepo.save(newUser);
    return {
      id: savedUser.id,
      username: savedUser.username,
      isAdmin: savedUser.isAdmin,
    };
  }

  /**
   * Update a user
   * @param userId User ID to update
   * @param updates Object containing fields to update
   * @returns The updated user
   */
  async updateUser(
    userId: string, 
    updates: { username?: string; password?: string; isAdmin?: boolean }
  ): Promise<UserDto> {
    const user = await this.userRepo.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException(`User with ID "${userId}" not found`);
    }

    // Update username if provided
    if (updates.username) {
      // Validate username format
      if (!/^[a-zA-Z0-9_]{3,20}$/.test(updates.username)) {
        throw new BadRequestException('Invalid username format');
      }

      // Check if new username already exists (if it's different from current)
      if (updates.username !== user.username) {
        const existingUser = await this.userRepo.findOne({ where: { username: updates.username } });
        if (existingUser) {
          throw new ConflictException('Username already exists');
        }
        user.username = updates.username;
      }
    }

    // Update password if provided
    if (updates.password) {
      // Validate password strength
      if (!/^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/.test(updates.password)) {
        throw new BadRequestException('Password too weak');
      }
      user.password = await bcrypt.hash(updates.password, 10);
    }

    // Update admin status if provided
    if (updates.isAdmin !== undefined) {
      user.isAdmin = updates.isAdmin;
    }

    const updatedUser = await this.userRepo.save(user);
    return {
      id: updatedUser.id,
      username: updatedUser.username,
      isAdmin: updatedUser.isAdmin,
    };
  }

  /**
   * Delete a user
   * @param userId User ID to delete
   * @returns Success message
   */
  async deleteUser(userId: string): Promise<{ message: string }> {
    const user = await this.userRepo.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException(`User with ID "${userId}" not found`);
    }

    await this.userRepo.remove(user);
    return { message: `User ${user.username} successfully deleted` };
  }
}
