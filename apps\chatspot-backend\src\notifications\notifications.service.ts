import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { FcmToken } from './entities/fcm-token.entity';
import * as admin from 'firebase-admin';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class NotificationsService {
  private readonly logger = new Logger(NotificationsService.name);
  private firebaseInitialized = false;

  constructor(
    @InjectRepository(FcmToken)
    private fcmTokenRepo: Repository<FcmToken>,
    private configService: ConfigService,
  ) {
    this.initializeFirebase();
  }

  private initializeFirebase() {
    try {
      // Check if Firebase is already initialized
      if (admin.apps.length === 0) {
        // Get Firebase configuration from environment variables
        const projectId = this.configService.get('FIREBASE_PROJECT_ID');
        const clientEmail = this.configService.get('FIREBASE_CLIENT_EMAIL');
        const privateKey = this.configService.get('FIREBASE_PRIVATE_KEY')?.replace(/\\n/g, '\n');

        // Log configuration (without sensitive data)
        this.logger.log(`Initializing Firebase with Project ID: ${projectId}`);

        // Validate configuration
        if (!projectId || !clientEmail || !privateKey) {
          this.logger.error('Firebase configuration is incomplete. Check your environment variables.');
          this.logger.error(`Project ID: ${projectId ? 'Set' : 'Missing'}`);
          this.logger.error(`Client Email: ${clientEmail ? 'Set' : 'Missing'}`);
          this.logger.error(`Private Key: ${privateKey ? 'Set' : 'Missing'}`);
          return;
        }

        // Initialize Firebase Admin SDK
        admin.initializeApp({
          credential: admin.credential.cert({
            projectId,
            clientEmail,
            privateKey,
          }),
        });

        this.firebaseInitialized = true;
        this.logger.log('Firebase Admin SDK initialized successfully');
      } else {
        this.firebaseInitialized = true;
        this.logger.log('Firebase Admin SDK already initialized');
      }
    } catch (error) {
      this.logger.error('Failed to initialize Firebase Admin SDK:', error);
      this.logger.error(error.message);
      if (error.code === 'app/invalid-credential') {
        this.logger.error('Invalid credentials. Check your service account key.');
      }
    }
  }

  async registerToken(username: string, token: string, deviceInfo?: string): Promise<FcmToken> {
    try {
      // Check if token already exists for this user
      const existingToken = await this.fcmTokenRepo.findOne({
        where: { username, token },
      });

      if (existingToken) {
        // Update existing token
        existingToken.is_active = true;
        existingToken.device_info = deviceInfo || existingToken.device_info;
        existingToken.last_used_at = new Date();
        return this.fcmTokenRepo.save(existingToken);
      }

      // Create new token
      const fcmToken = this.fcmTokenRepo.create({
        username,
        token,
        device_info: deviceInfo,
        is_active: true,
        last_used_at: new Date(),
      });

      return this.fcmTokenRepo.save(fcmToken);
    } catch (error) {
      this.logger.error(`Failed to register FCM token for user ${username}:`, error);
      throw error;
    }
  }

  async getActiveTokensForUser(username: string): Promise<FcmToken[]> {
    try {
      return this.fcmTokenRepo.find({
        where: { username, is_active: true },
      });
    } catch (error) {
      this.logger.error(`Failed to get active FCM tokens for user ${username}:`, error);
      throw error;
    }
  }

  async deactivateToken(token: string): Promise<void> {
    try {
      await this.fcmTokenRepo.update(
        { token },
        { is_active: false }
      );
    } catch (error) {
      this.logger.error(`Failed to deactivate FCM token:`, error);
      throw error;
    }
  }

  async sendNotification(
    username: string,
    title: string,
    body: string,
    data: Record<string, string> = {}
  ): Promise<boolean> {
    if (!this.firebaseInitialized) {
      this.logger.error('Firebase Admin SDK not initialized');

      // Try to initialize Firebase again
      this.initializeFirebase();

      // Check if initialization was successful
      if (!this.firebaseInitialized) {
        this.logger.error('Failed to initialize Firebase Admin SDK. Cannot send notification.');
        return false;
      }
    }

    try {
      // Get active tokens for the user
      const tokens = await this.getActiveTokensForUser(username);

      if (!tokens || tokens.length === 0) {
        this.logger.log(`No active FCM tokens found for user ${username}`);
        return false;
      }

      // Extract token strings
      const tokenStrings = tokens.map(t => t.token);

      // Send notifications individually to each token
      const successfulTokens: string[] = [];
      const failedTokens: string[] = [];

      // Process each token individually
      for (const token of tokenStrings) {
        try {
          const message = {
            notification: {
              title,
              body,
            },
            data,
            token, // Send to individual token
            webpush: {
              fcm_options: {
                link: `${this.configService.get('FRONTEND_URL', 'https://chatspot-messenger.netlify.app')}/chat`,
              },
              notification: {
                icon: '/icons/app-icon-192x192.png',
              },
            },
          };

          // Send message to a single device
          const response = await admin.messaging().send(message);

          if (response) {
            successfulTokens.push(token);
          }
        } catch (error) {
          failedTokens.push(token);
          this.logger.error(`Failed to send notification to token: ${token}`, error);
        }
      }

      this.logger.log(`Sent notification to ${successfulTokens.length} devices for user ${username}`);

      // Handle failed tokens
      if (failedTokens.length > 0) {
        this.logger.warn(`Failed to send notification to ${failedTokens.length} devices for user ${username}`);

        // Deactivate failed tokens
        for (const token of failedTokens) {
          await this.deactivateToken(token);
        }
      }

      return successfulTokens.length > 0;
    } catch (error) {
      this.logger.error(`Failed to send notification to user ${username}:`, error);
      return false;
    }
  }
}
