.settings-container {
  max-width: 500px;
  margin: 2rem auto;
  padding: 1rem;
  background: var(--md-sys-color-surface);
  border-radius: 12px;
  box-shadow: var(--elevation-2);
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  font-size: 17px;
  background-color: #f1f0f0;;
}


@media (min-width: 768px) {
.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0px 20px;
  font-size: 17px;
}
}



.material-btn {
  color: var(--tint-color);
  border: none;
  border-radius: 20px;
  padding: 0.5rem 0rem;
  cursor: pointer;
  height: 42px;
}

.danger-btn {
  color: #ff3b30;
}

.material-switch {
  position: relative;
  display: inline-block;
  width: 42px;
  height: 26px;
}

.material-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.material-switch .slider {
  position: absolute;
  cursor: pointer;
  top: 0; left: 0;
  right: 0; bottom: 0;
  background-color: #ccc;
  border-radius: 34px;
  transition: 0.4s;
}

.material-switch input:checked + .slider {
  background-color: var(--primary-color);
}

.material-switch .slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  border-radius: 50%;
  transition: 0.4s;
}

.material-switch input:checked + .slider:before {
  transform: translateX(16px);
}

.material-switch .slider {
  position: absolute;
  cursor: pointer;
  top: 0; left: 0;
  right: 0; bottom: 0;
  background-color: #ccc;
  border-radius: 34px;
  transition: 0.4s;
}

.material-switch input:checked + .slider {
  background-color: var(--primary-color);
}

.material-switch .slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  border-radius: 50%;
  transition: 0.4s;
}

.material-switch input:checked + .slider:before {
  transform: translateX(16px);
}

.settings-window {

}

.settings-header {
  display: flex;
  align-items: right;
  padding: 8px 10px;
  background-color: var(--card-background);
  position: sticky;
  top: 0;
  z-index: 5;
  justify-content: right;
      font-size: small;
    font-weight: 600;
    color: #666;
}


.close-button-settings {
  font-size: 34px;
}

.settings-user-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 30px;
}

.user-avatar-large {
  display: flex;
  width: 80px;
  height: 80px;
  border-radius: 50px;
  justify-content: center;
  font-size: xx-large;
  color: var(--primary-color);
  background-color: var(--primary-color-tint);
  font-weight: bold;
  align-items: center;
}

.username-display {
  font-size: x-large;
}

.setting-selected .user-avatar{
  background-color: var(--primary-color);
  color: var(--primary-color-light);
  border-radius: 45px;
}

.setting-selected{
background-color: var(--tint-color-light)
}

.setting-selected .current-user{
  color: var(--primary-color-dark);
}

.setting-group{
  margin: 30px 0px;

}
.setting-group p{
  text-align: left;
  padding: 5px 20px;
  font-weight: 600;
}

.settings-form {

}

.form-group {

}

.form-group.toggle {

}

.settings-actions {
  padding: 20px 150px;
}

.logout-btn {

}
