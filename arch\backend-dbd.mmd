erDiagram
  users {
    uuid id PK
    string username
    string password
    boolean isAdmin
  }
  refresh_tokens {
    uuid id PK
    text token
    uuid userId FK
    timestamp expiresAt
    boolean isRevoked
    string deviceId
    string userAgent
    string ipAddress
    timestamp createdAt
  }
  messages {
    uuid id PK
    string sender_username
    string receiver_username
    text message
    timestamp timestamp
    string status
    timestamp delivered_at
    string type
    string client_message_id
    string media_id FK
    string media_url
    string media_type
    string media_filename
    bigint media_file_size
  }
  media {
    uuid id PK
    string original_filename
    string filename
    string mime_type
    bigint file_size
    string media_type
    string file_path
    string uploaded_by
    int width
    int height
    float duration
    text thumbnail_path
    string s3_key
    timestamp uploaded_at
  }
  fcm_tokens {
    uuid id PK
    string username FK
    text token
    string device_info
    boolean is_active
    timestamp created_at
    timestamp updated_at
    timestamp last_used_at
  }
  users ||--o{ refresh_tokens : "id=userId"
  users ||--o{ messages : "username=sender_username"
  users ||--o{ messages : "username=receiver_username"
  users ||--o{ media : "username=uploaded_by"
  media ||--o{ messages : "id=media_id"
  users ||--o{ fcm_tokens : "username" 