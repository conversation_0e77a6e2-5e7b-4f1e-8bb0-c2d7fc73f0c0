import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Message } from './message.entity';

@Injectable()
export class MessageService {
  constructor(
    @InjectRepository(Message)
    private messageRepo: Repository<Message>,
  ) {}

  async savePendingMessage(data: Partial<Message>) {
    const msg = this.messageRepo.create(data);
    return this.messageRepo.save(msg);
  }

  async markAsDelivered(id: string) {
    try {
      const result = await this.messageRepo.update(id, {
        status: 'delivered',
        delivered_at: new Date(),
      });

      if (result.affected === 0) {
        console.warn(`No message with ID ${id} was found to mark as delivered`);
      }

      return result;
    } catch (error) {
      console.error(`Error marking message ${id} as delivered:`, error);
      throw error;
    }
  }

  async delete(id: string) {
    return this.messageRepo.delete(id);
  }

  // This method is deprecated and will be removed in future versions
  async getPendingMessagesForUser(userId: string) {
    console.warn('getPendingMessagesForUser is deprecated. Use getPendingMessagesForUsername instead.');
    return [];
  }

  async getPendingMessagesForUsername(username: string) {
    try {
      console.log(`Fetching pending messages for username: ${username}`);
      const messages = await this.messageRepo.find({
        where: { receiver_username: username, status: 'sent' },
      });

      console.log(`Found ${messages.length} pending messages for ${username}`);
      if (messages.length > 0) {
        messages.forEach((msg, index) => {
          console.log(`Pending message ${index + 1}/${messages.length}: ID=${msg.id}, From=${msg.sender_username}, Type=${msg.type}`);
        });
      }

      return messages;
    } catch (error) {
      console.error(`Error fetching pending messages for ${username}:`, error);
      throw error;
    }
  }

  async getAllMessagesForUsername(username: string) {
    try {
      console.log(`Fetching all messages for username: ${username}`);
      const messages = await this.messageRepo.find({
        where: [
          { sender_username: username },
          { receiver_username: username }
        ],
        order: { timestamp: 'ASC' }
      });

      console.log(`Found ${messages.length} total messages for ${username}`);
      return messages;
    } catch (error) {
      console.error(`Error fetching all messages for ${username}:`, error);
      throw error;
    }
  }

  async getMessagesForConversation(username1: string, username2: string) {
    try {
      console.log(`Fetching messages for conversation between ${username1} and ${username2}`);
      const messages = await this.messageRepo.find({
        where: [
          { sender_username: username1, receiver_username: username2 },
          { sender_username: username2, receiver_username: username1 }
        ],
        order: { timestamp: 'ASC' }
      });

      console.log(`Found ${messages.length} messages for conversation`);
      return messages;
    } catch (error) {
      console.error(`Error fetching conversation messages:`, error);
      throw error;
    }
  }

  async deleteDeliveredMessages(username: string) {
    try {
      console.log(`Deleting delivered messages for user: ${username}`);
      const result = await this.messageRepo.delete({
        receiver_username: username,
        status: 'delivered'
      });

      console.log(`Deleted ${result.affected || 0} delivered messages for ${username}`);
      return result;
    } catch (error) {
      console.error(`Error deleting delivered messages for ${username}:`, error);
      throw error;
    }
  }


}