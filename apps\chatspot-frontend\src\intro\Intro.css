:root {
  --bg-light: #f9f9f9;
  --bg-white: #ffffff;
  --text-main: #1e1e1e;
  --text-subtle: #555;
  --border-color: #e0e0e0;
}


body, html {
  margin: 0;
  padding: 0;
    font-family: 'Outfit', sans-serif;
  background-color: var(--bg-white);
  color: var(--text-main);
}
html, body {
  height: 100%;
  overflow: auto;
}


/* Global container for pages */
.page-container {
  max-width: 960px;
  margin: 0 auto;
  width: 100%;
}

header .large-banner{
    object-fit: contain;



}

/* Full-width sections (nav + features) */
.full-width-section {
  width: 100%;
  background: var(--some-background); /* can be gradient/image/color */
}

/* Centered content inside full-width sections */
.section-inner {
  max-width: 960px;
  margin: 0 auto;
  padding: 1rem 0rem;
  width: 100%;
}

.cover-picture{
  background: radial-gradient(circle at top left,transparent 25%,var(--tint-color-light) 25.5%, var(--tint-color-light) 36%, transparent 37%, transparent 100%),radial-gradient(circle at bottom right,transparent 34%,var(--tint-color-light) 34.5%, var(--tint-color-light) 45.5%, transparent 46%, transparent 100%);
        background-size: 3em 3em;
        background-color: #ffffff;
        opacity: 1
}


/* Navbar */
.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 0;
}

.navbar-outside{
  position: sticky;
  top: 0;
  z-index: 100;
  background-color: var(--bg-white);
  border-bottom: 1px solid var(--border-color);
}

.navbar-brand {
  font-size: 1.5rem;
  font-weight: 700;
}

/* CTA Button */
.cta-button {
  background-color: var(--primary-color);
  color: white;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  border: none;
  border-radius: 45px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.cta-button:hover {
  background-color: #1e40af; /* darker indigo */
}

/* Rounded style helper */
.rounded-large {
  border-radius: 45px;
}

/* Hero */
.hero {
  text-align: center;
  padding: 4rem 0px;
}

.hero h1 {
  font-size: 5rem;
  font-weight: 800;
  margin-bottom: 1rem;
  background-color: var(--secondary-color-light);
  display: inline;
  color: var(--shade-color);
}

.subheadline {
  font-size: 1.2rem;
  color: var(--text-subtle);
  margin: 2rem;
}

/* Image Placeholder */
.image-placeholder {
  background-color: #d3d3d3;
  color: #555;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9rem;
  border-radius: 12px;
  margin: 2rem auto;
  text-align: center;
}

.large-banner {
  height: 500px;
  width: 100%;
}

.medium-banner {
  height: 200px;
  width: 100%;
}

/* Features */
.features {
  padding: 4rem 1rem;
  background-color: var(--primary-color-tint);
  text-align: center;
}

@media (min-width: 768px) {
  .feature-grid {
  display: flex;
  gap: 2rem;
  margin-top: 2rem;
}
}

@media (max-width: 768px) {
  .feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.image-highlight-with-text h2{
  font-size: 1rem;
}

.image-highlight-with-text {
  height: 300px;
}
}

.feature-grid {

}

.feature-card {
  background-color: var(--secondary-color-light);
  padding: 1.5rem;
  border: 1px solid var(--primary-color-light);
  border-radius: 16px;
  transition: transform 0.2s ease;
  text-align: left;
}
.feature-card h3{
  color: var(--secondary-color);
}
.feature-card p{
  color: var(--shade-color);
}

.feature-card:hover {
  transform: scale(1.03);
}

/* Other Sections */
section {
  padding: 4rem 0rem;
}
section h4{
 text-align: center;
}

section h2 {
  font-size: 2rem;
  margin-bottom: 1rem;
  text-align: center;
}

section p {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
  font-size: 1.1rem;
  color: var(--text-subtle);
}

ul {
  list-style-type: disc;
  padding-left: 2rem;
  max-width: 700px;
  margin: 2rem auto 0;
  text-align: left;
}

ul li {
  margin-bottom: 0.75rem;
}

/* CTA Final Section */
.final-cta {
  background-color: var(--bg-light);
  text-align: center;
}

.final-cta h2 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 1rem;
}

.final-cta p {
  font-size: 1.2rem;
  margin-bottom: 2rem;
}

/* Footer */
.footer {
  text-align: center;
  padding: 2rem 1rem;
  font-size: 0.9rem;
  color: #888;
  border-top: 1px solid var(--border-color);
}

/* Animations (optional) */
.fade-in {
  animation: fadeInUp 0.6s ease forwards;
  opacity: 0;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(12px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in {
  animation: slideIn 1s ease forwards;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@media (max-width: 768px) {
.navbar-outside, .page-container{
  padding: 0px 15px;
}

.feature-row.reverse {
  flex-direction: none;
}

.hero h1 {
  font-size: 2rem;
  font-weight: 800;
  margin-bottom: 1rem;
}
section h2{
    font-size: 1.5rem;
}
.final-cta h2 {
  font-size: 1.5rem;
}


  .feature-img {
  height: 340px;
}
.cloud img{
  height: 100px;
}
}

.image-banner {
  display: block;
  object-fit: cover;
  border-radius: 12px;
  text-align: center;
}

/* Shared styling for both placeholder divs and img banners */
.large-banner,
.medium-banner {
  display: block;
  width: 100%;
  border-radius: 12px;
  object-fit: cover;
}

/* Specific height for large and medium banners */
.large-banner {
  height: 400px;
}

.medium-banner {
  height: 200px;
}


.image-highlight-with-text {
  position: relative;
  background-image: url('./images/2.jpeg'); /* adjust path if needed */
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  border-radius: 16px;
  margin: 3rem 0px;
  text-align: center;
  color: white;
}



.overlay-text {
  padding: 2rem;
  border-radius: 12px;
  max-width: 800px;
}

.overlay-text h2 {
  margin-bottom: 1rem;
  color: var(--primary-color);
  background-color: #fff;;
  display: inline-block;
}

.overlay-text p {
  font-size: 1.1rem;
  line-height: 1.6;
    color: var(--primary-color-light);

}

.image-banner-pair {
  display: flex;
  justify-content: center;
  align-items: flex-end;
  gap: 1rem;
  margin-top: 2rem;
  flex-wrap: wrap;
}

.image-banner-pair .banner {
  width: 45%;
  max-width: 500px;
  height: auto;
  border-radius: 16px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.overlap-left {
  transform: translateX(-10px);
  z-index: 2;
}

.overlap-right {
  transform: translateX(10px);
}










.technology-section {
  padding: 4rem 0rem;
  max-width: 1280px;
  margin: 0 auto;
}

.feature-row {
  display: flex;
  flex-direction: column; /* mobile first */
  align-items: center;
  gap: 2rem;
}

.feature-image-wrapper,
.feature-text {
  width: 100%;
  text-align: center;
}

.feature-img {
  width: auto;
  max-width: 500px;
  border-radius: 12px;
  box-shadow: 0 6px 18px rgba(0, 0, 0, 0.08);
}



/* ✅ Desktop version: min-width 768px */
@media (min-width: 768px){

  .feature-img {
  height: 550px;
}
  .feature-row {
    flex-direction: row;
    align-items: center;
    justify-content: space-evenly;
    gap: 3rem;
  }

  .feature-row.reverse {
  flex-direction: row-reverse;
}

  .feature-image-wrapper,
  .feature-text {
    width: 50%;
    text-align: left;
  }

  .feature-image-wrapper {
    display: flex;
    justify-content: center;
  }

  .feature-text {
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  .text-align-reverse{
  text-align: right;
}
.text-align-straight{
  text-align: left;
}
.image-highlight-with-text {
  height: 500px;
}

.overlay-text h2 {
  font-size: 2rem;
}
.cloud img{
  height: auto;
  box-shadow: none;
}

}



.feature-text p{
  text-align: ;
}



.last-section-button{
  margin-top: 20px;
}
