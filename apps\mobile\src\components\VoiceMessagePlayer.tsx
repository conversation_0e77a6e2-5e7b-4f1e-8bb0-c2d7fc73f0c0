import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useTheme, radius } from '../theme';
import { voiceRecordingService } from '../services/voiceRecordingService';

interface VoiceMessagePlayerProps {
  audioUri: string;
  duration?: number;
  isMine: boolean;
  onPress?: () => void;
}

const VoiceMessagePlayer: React.FC<VoiceMessagePlayerProps> = ({
  audioUri,
  duration = 0,
  isMine,
  onPress
}) => {
  const { colors } = useTheme();
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [totalDuration, setTotalDuration] = useState(duration);
  const [isLoading, setIsLoading] = useState(false);

  const styles = createStyles(colors);

  useEffect(() => {
    return () => {
      // Cleanup when component unmounts
      if (isPlaying) {
        voiceRecordingService.stopPlayback();
      }
    };
  }, [isPlaying]);

  const handlePlayPause = async () => {
    if (isLoading) return;

    try {
      setIsLoading(true);

      if (isPlaying) {
        // Stop playback
        await voiceRecordingService.stopPlayback();
        setIsPlaying(false);
        setCurrentTime(0);
      } else {
        // Start playback
        await voiceRecordingService.playAudio(audioUri, (current, total) => {
          setCurrentTime(current);
          setTotalDuration(total);
          
          // Auto-stop when finished
          if (current >= total) {
            setIsPlaying(false);
            setCurrentTime(0);
          }
        });
        setIsPlaying(true);
      }
    } catch (error) {
      console.error('Error playing audio:', error);
      setIsPlaying(false);
    } finally {
      setIsLoading(false);
    }
  };

  const formatTime = (seconds: number): string => {
    return voiceRecordingService.formatTime(seconds);
  };

  const getProgressPercentage = (): number => {
    if (totalDuration === 0) return 0;
    return (currentTime / totalDuration) * 100;
  };

  return (
    <TouchableOpacity
      style={[
        styles.container,
        isMine ? styles.sentContainer : styles.receivedContainer
      ]}
      onPress={onPress}
      activeOpacity={0.7}
    >
      {/* Play/Pause Button */}
      <TouchableOpacity
        style={[
          styles.playButton,
          isMine ? styles.sentPlayButton : styles.receivedPlayButton
        ]}
        onPress={handlePlayPause}
        disabled={isLoading}
      >
        <Icon
          name={isLoading ? "loading" : isPlaying ? "pause" : "play"}
          size={20}
          color={isMine ? colors.white : colors.primary}
        />
      </TouchableOpacity>

      {/* Waveform/Progress Area */}
      <View style={styles.progressContainer}>
        {/* Progress Bar */}
        <View style={styles.progressBarContainer}>
          <View
            style={[
              styles.progressBar,
              isMine ? styles.sentProgressBar : styles.receivedProgressBar
            ]}
          >
            <View
              style={[
                styles.progressFill,
                isMine ? styles.sentProgressFill : styles.receivedProgressFill,
                { width: `${getProgressPercentage()}%` }
              ]}
            />
          </View>
        </View>

        {/* Time Display */}
        <View style={styles.timeContainer}>
          <Text
            style={[
              styles.timeText,
              isMine ? styles.sentTimeText : styles.receivedTimeText
            ]}
          >
            {isPlaying ? formatTime(currentTime) : formatTime(totalDuration)}
          </Text>
        </View>
      </View>

      {/* Voice Icon */}
      <View style={styles.voiceIconContainer}>
        <Icon
          name="microphone"
          size={16}
          color={isMine ? colors.white + '80' : colors.textSecondary}
        />
      </View>
    </TouchableOpacity>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 18,
    minWidth: 200,
    maxWidth: 280,
  },
  sentContainer: {
    backgroundColor: colors.primary,
  },
  receivedContainer: {
    backgroundColor: colors.cardBackgroundDS,
    borderWidth: 1,
    borderColor: colors.border,
  },
  playButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  sentPlayButton: {
    backgroundColor: colors.white + '20',
  },
  receivedPlayButton: {
    backgroundColor: colors.primary + '20',
  },
  progressContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  progressBarContainer: {
    marginBottom: 4,
  },
  progressBar: {
    height: 3,
    borderRadius: 1.5,
    overflow: 'hidden',
  },
  sentProgressBar: {
    backgroundColor: colors.white + '30',
  },
  receivedProgressBar: {
    backgroundColor: colors.gray100,
  },
  progressFill: {
    height: '100%',
    borderRadius: 1.5,
  },
  sentProgressFill: {
    backgroundColor: colors.white,
  },
  receivedProgressFill: {
    backgroundColor: colors.primary,
  },
  timeContainer: {
    alignItems: 'flex-start',
  },
  timeText: {
    fontSize: 12,
    fontFamily: 'Outfit-Regular',
  },
  sentTimeText: {
    color: colors.white + '80',
  },
  receivedTimeText: {
    color: colors.textSecondary,
  },
  voiceIconContainer: {
    marginLeft: 8,
  },
});

export default VoiceMessagePlayer;
