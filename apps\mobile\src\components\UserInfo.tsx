import React, { useMemo } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import { useAppSelector } from '../hooks/redux';
import { selectUserEmojiReaction } from '../redux/slices/emojiReactionSlice';
import { useTheme } from '../theme';

interface UserInfoProps {
  userId: string; // This is now actually a username
  className?: string;
  lastMessage?: string; // Optional prop for last message
  status?: string; // Optional prop for status
  showEmojiReactions?: boolean; // Whether to show emoji reactions in room list
  disableEmoji?: boolean; // Whether to disable emoji visibility completely
  onClick?: () => void; // Optional click handler for the user info component
  style?: any; // Additional styles
}

const UserInfo: React.FC<UserInfoProps> = ({
  userId,
  lastMessage,
  status,
  showEmojiReactions = true, // Default to true
  disableEmoji = false, // Default to false (emojis are visible)
  onClick,
  style
}) => {
  const { colors } = useTheme();

  // Memoize selector function to prevent infinite rerenders
  const emojiReactionSelector = useMemo(() =>
    showEmojiReactions && !disableEmoji ? selectUserEmojiReaction(userId) : () => ({ emoji: null, mood: null }),
    [userId, showEmojiReactions, disableEmoji]
  );

  // Check if this user has an active emoji reaction (only if emojis are not disabled)
  const emojiReaction = useAppSelector(emojiReactionSelector);

  // Display first letter of username as avatar
  const getAvatarText = () => {
    return userId ? userId.charAt(0).toUpperCase() : '?';
  };

  // Display username
  const getDisplayName = () => {
    return userId || 'Unknown User';
  };

  const renderTypingIndicator = () => {
    return (
      <View style={styles.typingContainer}>
        <Text style={styles.typingText}>Typing</Text>
        <View style={styles.typingIndicator}>
          <View style={[styles.typingDot, styles.typingDot1]} />
          <View style={[styles.typingDot, styles.typingDot2]} />
          <View style={[styles.typingDot, styles.typingDot3]} />
        </View>
      </View>
    );
  };

  const styles = createStyles(colors);

  return (
    <View style={[styles.container, style]}>
      <TouchableOpacity style={styles.avatar} onPress={onClick}>
        <Text style={styles.avatarText}>{getAvatarText()}</Text>
      </TouchableOpacity>
      
      <View style={styles.details}>
  <View style={styles.topRow}>
    <TouchableOpacity onPress={onClick}>
      <Text style={styles.userName}>{getDisplayName()}</Text>
    </TouchableOpacity>
  </View>

  <View style={styles.bottomRow}>
    {/* Show emoji or last message */}
    {emojiReaction && emojiReaction.emoji ? (
      <View style={styles.emojiReactionContainer}>
        <Text style={styles.emojiReaction}>{emojiReaction.emoji}</Text>
      </View>
    ) : lastMessage ? (
      <Text style={styles.lastMessage} numberOfLines={1}>
        {lastMessage}
      </Text>
    ) : null}

    {/* Status goes below or replaces message */}
    {status && (
      <View style={styles.statusContainer}>
        {status === 'Typing' ? (
          renderTypingIndicator()
        ) : (
          <Text style={styles.status}>{status}</Text>
        )}
      </View>
    )}
  </View>
</View>

    </View>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 20,
    padding:5,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 25,
    backgroundColor: colors.toneLight3,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  avatarText: {
    color: colors.toneLight1,
    fontSize: 20,
    fontFamily: 'Outfit-Bold',
  },
  details: {
  flex: 1,
  height: 40, // slightly larger than avatar for text breathing room
  justifyContent: 'center',
},

  userName: {
  fontSize: 14,
  fontFamily: 'Outfit-Medium',
  color: colors.text,
},
lastMessage: {
  fontSize: 14,
  fontFamily: 'Outfit-Regular',
  color: colors.textSecondary,
},

  emojiReactionContainer: {
    marginBottom: 2,
  },
  emojiReaction: {
    fontSize: 16,
    fontFamily: 'Outfit-Regular',
  },
  statusContainer: {
    marginTop: 0,
  },
  status: {
    fontSize: 12,
    fontFamily: 'Outfit-Regular',
    color: colors.textSecondary,
  },
  typingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  typingText: {
    fontSize: 12,
    fontFamily: 'Outfit-Regular',
    color: colors.primary,
    marginRight: 6,
  },
  typingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  typingDot: {
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: colors.primary,
    marginHorizontal: 1,
  },
topRow: {
  flex: 1,
  justifyContent: 'center',
},
bottomRow: {
  flex: 1,
  justifyContent: 'center',
},


  typingDot1: {
    // Animation would be added here in a real implementation
  },
  typingDot2: {
    // Animation would be added here in a real implementation
  },
  typingDot3: {
    // Animation would be added here in a real implementation
  },
});

export default UserInfo;
