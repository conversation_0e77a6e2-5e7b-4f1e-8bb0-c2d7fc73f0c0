.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: var(--card-background, white);
  border-radius: var(--radius-md, 8px);
  width: 95%;
  max-width: 400px;
  box-shadow: var(--shadow-lg, 0 10px 15px rgba(0, 0, 0, 0.1));
  animation: modalFadeIn 0.3s ease-out;
}

@media (min-width: 768px) {
  .modal-content {
    width: 90%;
    max-width: 450px;
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid var(--border-color, #eee);
}

.modal-header h3 {
  margin: 0;
  color: var(--primary-color, #F0503E);
  font-size: 18px;
  font-weight: 600;
}

.close-button {
  background: none;
  border: none;
  font-size: 20px;
  color: var(--text-secondary, #666);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.close-button:hover {
  background-color: var(--hover-background, #f5f5f5);
}

.modal-body {
  padding: 20px;
}

.media-options-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
}

.media-option-button {
  display: flex;
  align-items: center;
  padding: 16px;
  background: var(--card-background, white);
  border: 1px solid var(--border-color, #eee);
  border-radius: var(--radius-md, 8px);
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
  width: 100%;
}

.media-option-button:hover {
  background-color: var(--hover-background, #f8f9fa);
  border-color: var(--primary-color, #F0503E);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.media-option-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  color: white;
  font-size: 20px;
  flex-shrink: 0;
}

.media-option-content {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.media-option-label {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary, #333);
  margin-bottom: 4px;
}

.media-option-description {
  font-size: 14px;
  color: var(--text-secondary, #666);
  line-height: 1.4;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Mobile optimizations */
@media (max-width: 480px) {
  .modal-content {
    width: 95%;
    margin: 20px;
  }
  
  .modal-header {
    padding: 12px 16px;
  }
  
  .modal-body {
    padding: 16px;
  }
  
  .media-option-button {
    padding: 12px;
  }
  
  .media-option-icon {
    width: 40px;
    height: 40px;
    margin-right: 12px;
    font-size: 18px;
  }
  
  .media-option-label {
    font-size: 15px;
  }
  
  .media-option-description {
    font-size: 13px;
  }
}
