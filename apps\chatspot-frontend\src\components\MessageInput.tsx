import React, { useState, useRef, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { sendMessageRequest, selectConnected, selectError } from '../redux/slices/socketSlice';
import './MessageInput.css';
import { RootState } from '../redux/store';
import EmojiBar from './EmojiBar';
import { selectEmojiReactionsEnabled } from '../redux/slices/emojiReactionSlice'; // adjust path if needed
import { mediaUploadService } from '../services/mediaUploadService';
import MediaPickerModal from './MediaPickerModal';

interface MessageInputProps {
  receiverUsername: string;
  onFocus?: () => void;
  onBlur?: () => void;
}

const MessageInput: React.FC<MessageInputProps> = ({ receiverUsername, onFocus, onBlur }) => {
  const dispatch = useDispatch();
  const connected = useSelector(selectConnected);
  const error = useSelector(selectError);

  const [message, setMessage] = useState<string>('');
  const [sendStatus, setSendStatus] = useState<{ success: boolean, message: string } | null>(null);
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const [pendingMedia, setPendingMedia] = useState<any>(null);
  const [showMediaPicker, setShowMediaPicker] = useState<boolean>(false);
  const inputRef = useRef<HTMLTextAreaElement>(null);
  const emojiReactionsEnabled = useSelector(selectEmojiReactionsEnabled);



  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.scrollIntoView({ block: 'nearest' });
    }
  }, [message]);



  // Watch for errors from Redux
  useEffect(() => {
    if (error && error.includes('Failed to send message')) {
      setSendStatus({
        success: false,
        message: error
      });
    }
  }, [error]);

  // Clear send status after 3 seconds
  useEffect(() => {
    if (sendStatus) {
      const timer = setTimeout(() => {
        setSendStatus(null);
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [sendStatus]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if ((!message.trim() && !pendingMedia) || !receiverUsername || !connected) {
      if (!connected) {
        setSendStatus({
          success: false,
          message: 'Not connected to server'
        });
      }
      return;
    }

    // Send as a text+media message if pendingMedia is set
    dispatch(sendMessageRequest({
      receiverUsername,
      messageText: message.trim(),
      messageType: pendingMedia ? 'media' : 'text',
      mediaData: pendingMedia || undefined
    }));
    setMessage('');
    setPendingMedia(null);

    // Focus back on the input after sending
    if (inputRef.current) {
      inputRef.current.focus();
      inputRef.current.style.height = 'auto';
    }
  };

  // Typing indicator state
  const [isTyping, setIsTyping] = useState<boolean>(false);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Send typing indicator
  const sendTypingIndicator = (typing: boolean) => {
    if (connected && receiverUsername) {
      dispatch(sendMessageRequest({
        receiverUsername,
        messageText: typing ? 'typing' : 'stopped_typing',
        messageType: 'typing'
      }));
    }
  };

  // Handle textarea height adjustment and typing indicator
  const handleInput = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const target = e.target;
    const newValue = target.value;
    setMessage(newValue);

    // Reset height to auto to properly calculate the new height
    target.style.height = 'auto';

    // Set new height based on scrollHeight (with a max height)
    // Ensure a minimum height of 44px (or 40px on mobile)
    // const minHeight = window.innerWidth <= 767 ? 40 : 40;
    // const newHeight = Math.max(minHeight, Math.min(target.scrollHeight, 120));
    // target.style.height = `${newHeight}px`;

    // Handle typing indicator
    if (newValue.trim() && !isTyping) {
      // User started typing
      setIsTyping(true);
      sendTypingIndicator(true);
    } else if (!newValue.trim() && isTyping) {
      // User stopped typing
      setIsTyping(false);
      sendTypingIndicator(false);
    }

    // Reset typing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Set a timeout to stop the typing indicator after 3 seconds of inactivity
    typingTimeoutRef.current = setTimeout(() => {
      if (isTyping) {
        setIsTyping(false);
        sendTypingIndicator(false);
      }
    }, 3000);
  };

  // Set initial height and handle window resize
  useEffect(() => {
    // const setInitialHeight = () => {
    //   if (inputRef.current) {
    //     const minHeight = window.innerWidth <= 767 ? 40 : 40;
    //     inputRef.current.style.height = `${minHeight}px`;
    //   }
    // };

    // Set initial height
    // setInitialHeight();

    // Add resize listener to adjust height on window resize
    // window.addEventListener('resize', setInitialHeight);

    // Clean up
    return () => {
      // window.removeEventListener('resize', setInitialHeight);

      // Clear typing timeout and send stopped typing on unmount
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }

      if (isTyping && connected && receiverUsername) {
        sendTypingIndicator(false);
      }
    };
  }, [isTyping, connected, receiverUsername]);

  // Handle Enter key to submit (Shift+Enter for new line)
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    console.log(e)
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  // Handle emoji click
  const handleEmojiClick = (emoji: string) => {

  };

  // Handle emoji long press
  const handleEmojiLongPress = (emoji: string) => {
    if (connected && receiverUsername) {
      // Get the mood name from the emoji
      let mood = '';
      switch (emoji) {
        case '😊': mood = 'happy'; break;
        case '😂': mood = 'laughing'; break;
        case '😡': mood = 'angry'; break;
        case '😢': mood = 'sad'; break;
        case '❤️': mood = 'love'; break;
        case '👍': mood = 'thumbsUp'; break;
        default: mood = 'feeling';
      }
      console.log('emoji pressed')
      // Send emoji reaction message with emoji and mood
      dispatch(sendMessageRequest({
        receiverUsername,
        messageText: `${emoji}:${mood}`,
        messageType: 'emoji_reaction'
      }));
    }
  };

  // Handle emoji release
  const handleEmojiRelease = () => {
    if (connected && receiverUsername) {
      // Send message to stop showing emoji reaction
      dispatch(sendMessageRequest({
        receiverUsername,
        messageText: 'stopped_reaction',
        messageType: 'emoji_reaction'
      }));
    }
  };

  // Handle media upload - show modal directly
  const handleMediaUpload = () => {
    if (!connected || !receiverUsername || isUploading) {
      return;
    }

    setShowMediaPicker(true);
  };

  // Handle individual media type selections
  const handleMediaSelection = async (mediaType: 'image' | 'video' | 'audio' | 'document') => {
    setShowMediaPicker(false);
    setIsUploading(true);
    setSendStatus(null);

    try {
      let result;
      switch (mediaType) {
        case 'image':
          result = await mediaUploadService.pickImage();
          break;
        case 'video':
          result = await mediaUploadService.pickVideo();
          break;

        case 'document':
          result = await mediaUploadService.pickDocument();
          break;
        default:
          result = { success: false, error: 'Invalid media type' };
      }



      if (result.success && result.file) {
        setPendingMedia(result.file);
      } else if (result.error && result.error !== 'User cancelled') {
        setSendStatus({
          success: false,
          message: result.error
        });
      }
    } catch (error) {
      console.error('Media selection error:', error);
      setSendStatus({
        success: false,
        message: 'Failed to select media'
      });


    } finally {
      setIsUploading(false);
    }
  };

  const handleCloseMediaPicker = () => {
    setShowMediaPicker(false);
  };

  return (
    <div className="message-input-wrapper">
      {sendStatus && (
        <div className={`send-status ${sendStatus.success ? 'success' : 'error'}`}>{sendStatus.message}</div>
      )}

      {/* Emoji Bar */}
      {emojiReactionsEnabled && (
        <EmojiBar
          onEmojiClick={handleEmojiClick}
          onEmojiLongPress={handleEmojiLongPress}
          onEmojiRelease={handleEmojiRelease}
        />
      )}

      {/* Media Preview */}
      {pendingMedia && (
        <div style={{ display: 'flex', alignItems: 'center', margin: '8px 0 8px 0', padding: '0 8px' }}>
          {pendingMedia.type && pendingMedia.type.startsWith('image/') ? (
            <img src={pendingMedia.uri} alt={pendingMedia.name} style={{ width: 60, height: 60, borderRadius: 8, marginRight: 8, objectFit: 'cover' }} />
          ) : (
            <span style={{ fontSize: 32, marginRight: 8 }}>📄</span>
          )}
          <span style={{ flex: 1, overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>{pendingMedia.name}</span>
          <button type="button" onClick={() => setPendingMedia(null)} style={{ marginLeft: 8, background: 'none', border: 'none', cursor: 'pointer' }}>
            <span style={{ fontSize: 20, color: '#c62828' }}>✕</span>
          </button>
        </div>
      )}

      <form className="message-input-form" onSubmit={handleSubmit}>
        <button
          type="button"
          onClick={handleMediaUpload}
          disabled={!connected || !receiverUsername || isUploading}
          className="media-button"
          title="Attach media"
        >
          {isUploading ? (
            <div className="upload-spinner"></div>
          ) : (
            <svg
              viewBox="0 0 24 24"
              width="20"
              height="20"
              fill="currentColor"
            >
              <path d="M19 7v2.99s-1.99.01-2 0V7h-3s.01-1.99 0-2h3V2h2v3h3v2h-3zm-3 4V8h-3V5H5c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2v-8h-3zM5 19l3-4 2 3 3-4 4 5H5z"/>
            </svg>
          )}
        </button>
        <textarea
          ref={inputRef}
          value={message}
          onChange={handleInput}
          onKeyDown={handleKeyDown}
          placeholder={connected ? (pendingMedia ? 'Add a caption...' : 'Type a message...') : 'Connect to send messages'}
          disabled={!connected || !receiverUsername}
          className="message-input"
          rows={1}
          onFocus={onFocus}
          onBlur={onBlur}
        />
        <button
          type="submit"
          disabled={!connected || (!message.trim() && !pendingMedia) || !receiverUsername}
          className="send-button"
          title="Send message"
        >
          <svg
            viewBox="0 0 12800 12780"
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            preserveAspectRatio="xMidYMid meet"
            fill="currentColor"
            style={{ transform: "rotate(-90deg)" }}
          >
            <path d="M2835 12432 c-752 -746 -2825 -2815 -2825 -2818 0 -2 1635 -4 3633
  -4 l3632 0 -3578 -3577 c-1967 -1968 -3577 -3580 -3577 -3583 0 -3 522 -527
  1160 -1165 l1160 -1160 3588 3588 c1973 1973 3590 3587 3594 3587 5 0 9 -1640
  10 -3643 l3 -3644 1583 1576 1582 1576 0 4808 0 4807 -4807 0 -4808 0 -350
  -348z"/>
          </svg>
        </button>
      </form>

      {/* Media Picker Modal */}
      <MediaPickerModal
        isVisible={showMediaPicker}
        onClose={handleCloseMediaPicker}
        onSelectImage={() => handleMediaSelection('image')}
        onSelectVideo={() => handleMediaSelection('video')}
        onSelectAudio={() => handleMediaSelection('audio')}
        onSelectDocument={() => handleMediaSelection('document')}
      />
    </div>
  );
};

export default MessageInput;
