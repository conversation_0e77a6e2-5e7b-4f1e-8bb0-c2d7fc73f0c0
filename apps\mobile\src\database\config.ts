import { Database } from '@nozbe/watermelondb';
import SQLiteAdapter from '@nozbe/watermelondb/adapters/sqlite';
import { schema } from './schema';
import { Room } from './models/Room';
import { Chat } from './models/Chat';

// Create the SQLite adapter
const adapter = new SQLiteAdapter({
  schema,
  // Optional: Enable JSI for better performance (requires additional setup)
  jsi: false,
  // Optional: Enable debugging
  onSetUpError: (error) => {
    console.error('Database setup error:', error);
  },
});

// Create the database
export const database = new Database({
  adapter,
  modelClasses: [Room, Chat],
});

// Helper function to generate room ID
export const getRoomId = (user1: string, user2: string): string => {
  // Sort usernames to ensure consistent room IDs
  const sortedUsers = [user1, user2].sort();
  return `${sortedUsers[0]}_${sortedUsers[1]}`;
};

// Helper function to get the other user in a room
export const getOtherUser = (roomId: string, currentUser: string): string => {
  const users = roomId.split('_');
  return users[0] === currentUser ? users[1] : users[0];
};
