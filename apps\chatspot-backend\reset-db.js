#!/usr/bin/env node

/**
 * SQLite Database Reset Script
 * 
 * This script helps reset the SQLite database for development purposes.
 * It removes the existing database file so the application can create a fresh one.
 */

const fs = require('fs');
const path = require('path');

const dbPath = path.join(__dirname, 'chatspot-dev.db');

console.log('🗄️  SQLite Database Reset Script');
console.log('================================');

if (fs.existsSync(dbPath)) {
  try {
    fs.unlinkSync(dbPath);
    console.log('✅ Database file deleted successfully');
    console.log(`   Removed: ${dbPath}`);
    console.log('');
    console.log('💡 Next steps:');
    console.log('   1. Start the backend: npm run start:localhost');
    console.log('   2. The database will be recreated automatically');
    console.log('   3. Create an admin user: npm run create-admin');
  } catch (error) {
    console.error('❌ Error deleting database file:', error.message);
    process.exit(1);
  }
} else {
  console.log('ℹ️  Database file does not exist');
  console.log(`   Looking for: ${dbPath}`);
  console.log('');
  console.log('💡 The database will be created when you start the backend');
}

console.log('');
console.log('🚀 Ready for development!');
