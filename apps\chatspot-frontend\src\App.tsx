import { Browser<PERSON>outer, Routes, Route, Navigate, useNavigate, useLocation } from 'react-router-dom'
import { useSelector, useDispatch } from 'react-redux'
import { useEffect, useState, useLayoutEffect } from 'react'
import { selectIsAuthenticated, selectAuthToken } from './redux/slices/authSlice'
import { connectRequest, disconnectRequest, selectConnected } from './redux/slices/socketSlice'
import Login from './components/Login'
import Register from './components/Register'
import Chat from './components/Chat'
import ChatRoom from './components/ChatRoom'
import SettingsPage from './components/SettingsPage'
import ChatDBTest from './components/ChatDBTest'
import SplashScreen from './components/SplashScreen'
import NavigationManager from './components/NavigationManager'
import './App.css'
import ProfileWrapper from './components/ProfileWrapper';
import IntroPage from './intro/IntroPage';


// Protected route component
interface ProtectedRouteProps {
  children: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const isAuthenticated = useSelector(selectIsAuthenticated);
  return isAuthenticated ? <>{children}</> : <Navigate to="/login" />;
};

const App: React.FC = () => {
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(true);
  const isAuthenticated = useSelector(selectIsAuthenticated);
  const authToken = useSelector(selectAuthToken);
  const connected = useSelector(selectConnected);

  // Initialize app
  useEffect(() => {
    const initApp = async () => {
      try {
        // Simulate loading time (you can remove this in production)
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Set loading to false when initialization is complete
        setLoading(false);
      } catch (error) {
        console.error('Failed to initialize app:', error);
        setLoading(false);
      }
    };

    initApp();
  }, [dispatch]);

  // Manage socket connection at the app level
  useEffect(() => {
    // Connect to socket when authenticated
    if (isAuthenticated && authToken) {
      console.log('Connecting to socket from App component');
      dispatch(connectRequest({ authToken }));
    }

    // Disconnect when the app unmounts or user logs out
    return () => {
      if (connected) {
        console.log('Disconnecting socket from App component');
        dispatch(disconnectRequest());
      }
    };
  }, [isAuthenticated, authToken, dispatch]);

  // Auto-reconnect mechanism
  useEffect(() => {
    let reconnectTimer: NodeJS.Timeout | null = null;

    // If authenticated but not connected, try to reconnect
    if (isAuthenticated && authToken && !connected) {
      console.log('Socket disconnected, attempting to reconnect...');
      reconnectTimer = setTimeout(() => {
        console.log('Reconnecting to socket...');
        dispatch(connectRequest({ authToken }));
      }, 3000); // Try to reconnect after 3 seconds
    }

    // Clean up timer on unmount
    return () => {
      if (reconnectTimer) {
        clearTimeout(reconnectTimer);
      }
    };
  }, [isAuthenticated, authToken, connected, dispatch]);

  // Show splash screen while loading
  if (loading) {
    return <SplashScreen />;
  }


const AppRoutes = () => {
  const location = useLocation();

  // Show IntroPage *without* layout for "/"
  if (location.pathname === "/") {
    return (
      <Routes>
        <Route path="/" element={<IntroPage />} />
      </Routes>
    );
  }

  // For all other paths, show layout
  return (
    <div className="app-container">
      <NavigationManager />
      <main className="app-content">
        <Routes>
          <Route path="/login" element={<Login />} />
          <Route path="/register" element={<Register />} />
          <Route path="/chat" element={<ProtectedRoute><Chat /></ProtectedRoute>} />
          <Route path="/chat/:username" element={<ProtectedRoute><ChatRoom /></ProtectedRoute>} />
          <Route path="/settings" element={<ProtectedRoute><SettingsPage /></ProtectedRoute>} />
          <Route path="/profile/:username" element={<ProtectedRoute><ProfileWrapper /></ProtectedRoute>} />
          <Route path="/test" element={<ChatDBTest />} />
          <Route path="*" element={<Navigate to="/login" />} />
        </Routes>
      </main>
    </div>
  );
};



return (
  <BrowserRouter>
    <AppRoutes />
  </BrowserRouter>
)




}



export default App
