import React from 'react';
import { withObservables } from '@nozbe/watermelondb/react';
import { of } from 'rxjs';
import MediaMessageItem from './MediaMessageItem';

interface MessageItemProps {
  message?: any;
  formatTime: (timestamp: number) => string;
  isLastInGroup: boolean;
}

const MessageItemBase: React.FC<MessageItemProps> = ({
  message,
  formatTime,
  isLastInGroup
}) => {
  if (!message) return null;

  if (message.type === 'clear_chat') {
    return (
      <div className="message-system">
        <div
          className={`message-content system ${
            message.status === 'sending' ? 'sending-gradient' : ''
          }`}
        >
          <p>Chat cleared by {message.is_mine ? 'you' : message.sender_username}</p>
          <span className="message-time">{formatTime(message.timestamp)}</span>
        </div>
      </div>
    );
  }

  if (message.type === 'typing') return null;

  // Handle media messages
  if (message.type === 'media') {
    return (
      <MediaMessageItem
        message={message}
        formatTime={formatTime}
        isLastInGroup={isLastInGroup}
      />
    );
  }

  const renderStatusIcon = () => {
    if (!message.isMine) return null;

    switch (message.status) {
      case 'sending':
        return (
          <span className="message-status">
            <div className="message-spinner"></div>
          </span>
        );
      case 'sent':
        return (
          <span className="message-status">
            <div className="tick-icon single-tick">✓</div>
          </span>
        );
      case 'delivered':
      case 'read':
        return (
          <span className="message-status">
            <div className="tick-icon double-tick">✓✓</div>
          </span>
        );
      default:
        return null;
    }
  };

  return (
    <div
      className={`message ${message.isMine ? 'sent' : 'received'} ${
        isLastInGroup ? 'last-in-group' : ''
      }`}
    >
      <div
        className={`message-content ${
          message.status === 'sending' ? 'sending-gradient' : ''
        }`}
      >
        <p>{message.message}</p>
        <div className="message-info">
          <span className="message-time">{formatTime(message.timestamp)}</span>
          {renderStatusIcon()}
        </div>
      </div>
    </div>
  );
};

const enhance = withObservables(['message'], ({ message }) => ({ message }));

export default enhance(MessageItemBase);
