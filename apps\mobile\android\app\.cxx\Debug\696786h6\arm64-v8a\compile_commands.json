[{"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dappmodules_EXPORTS -IC:/chatspot-messenger/apps/mobile/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -IC:/chatspot-messenger/apps/mobile/android/app/build/generated/autolinking/src/main/jni -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni -IC:/chatspot-messenger/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-bootsplash/android/build/generated/source/codegen/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-bootsplash/android/build/generated/source/codegen/jni/react/renderer/components/RNBootSplashSpec -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/react/renderer/components/RNImagePickerSpec -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-nitro-modules/android/build/generated/source/codegen/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-nitro-modules/android/build/generated/source/codegen/jni/react/renderer/components/NitroModulesSpec -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o CMakeFiles\\appmodules.dir\\C_\\chatspot-messenger\\apps\\mobile\\android\\app\\build\\generated\\autolinking\\src\\main\\jni\\autolinking.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\android\\app\\build\\generated\\autolinking\\src\\main\\jni\\autolinking.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\android\\app\\build\\generated\\autolinking\\src\\main\\jni\\autolinking.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dappmodules_EXPORTS -IC:/chatspot-messenger/apps/mobile/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -IC:/chatspot-messenger/apps/mobile/android/app/build/generated/autolinking/src/main/jni -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni -IC:/chatspot-messenger/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-bootsplash/android/build/generated/source/codegen/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-bootsplash/android/build/generated/source/codegen/jni/react/renderer/components/RNBootSplashSpec -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/react/renderer/components/RNImagePickerSpec -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-nitro-modules/android/build/generated/source/codegen/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-nitro-modules/android/build/generated/source/codegen/jni/react/renderer/components/NitroModulesSpec -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o CMakeFiles\\appmodules.dir\\OnLoad.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\OnLoad.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\OnLoad.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/chatspot-messenger/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\ComponentDescriptors.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\ComponentDescriptors.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\ComponentDescriptors.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/chatspot-messenger/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\EventEmitters.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\EventEmitters.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\EventEmitters.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/chatspot-messenger/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\Props.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\Props.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\Props.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/chatspot-messenger/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\ShadowNodes.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\ShadowNodes.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\ShadowNodes.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/chatspot-messenger/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\States.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\States.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\States.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/chatspot-messenger/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\rnasyncstorageJSI-generated.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\rnasyncstorageJSI-generated.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\rnasyncstorageJSI-generated.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/chatspot-messenger/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\rnasyncstorage-generated.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\rnasyncstorage-generated.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\rnasyncstorage-generated.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-bootsplash/android/build/generated/source/codegen/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-bootsplash/android/build/generated/source/codegen/jni/react/renderer/components/RNBootSplashSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNBootSplashSpec_autolinked_build\\CMakeFiles\\react_codegen_RNBootSplashSpec.dir\\RNBootSplashSpec-generated.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-bootsplash\\android\\build\\generated\\source\\codegen\\jni\\RNBootSplashSpec-generated.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-bootsplash\\android\\build\\generated\\source\\codegen\\jni\\RNBootSplashSpec-generated.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-bootsplash/android/build/generated/source/codegen/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-bootsplash/android/build/generated/source/codegen/jni/react/renderer/components/RNBootSplashSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNBootSplashSpec_autolinked_build\\CMakeFiles\\react_codegen_RNBootSplashSpec.dir\\react\\renderer\\components\\RNBootSplashSpec\\ComponentDescriptors.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-bootsplash\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNBootSplashSpec\\ComponentDescriptors.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-bootsplash\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNBootSplashSpec\\ComponentDescriptors.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-bootsplash/android/build/generated/source/codegen/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-bootsplash/android/build/generated/source/codegen/jni/react/renderer/components/RNBootSplashSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNBootSplashSpec_autolinked_build\\CMakeFiles\\react_codegen_RNBootSplashSpec.dir\\react\\renderer\\components\\RNBootSplashSpec\\EventEmitters.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-bootsplash\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNBootSplashSpec\\EventEmitters.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-bootsplash\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNBootSplashSpec\\EventEmitters.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-bootsplash/android/build/generated/source/codegen/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-bootsplash/android/build/generated/source/codegen/jni/react/renderer/components/RNBootSplashSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNBootSplashSpec_autolinked_build\\CMakeFiles\\react_codegen_RNBootSplashSpec.dir\\react\\renderer\\components\\RNBootSplashSpec\\Props.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-bootsplash\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNBootSplashSpec\\Props.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-bootsplash\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNBootSplashSpec\\Props.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-bootsplash/android/build/generated/source/codegen/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-bootsplash/android/build/generated/source/codegen/jni/react/renderer/components/RNBootSplashSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNBootSplashSpec_autolinked_build\\CMakeFiles\\react_codegen_RNBootSplashSpec.dir\\react\\renderer\\components\\RNBootSplashSpec\\RNBootSplashSpecJSI-generated.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-bootsplash\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNBootSplashSpec\\RNBootSplashSpecJSI-generated.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-bootsplash\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNBootSplashSpec\\RNBootSplashSpecJSI-generated.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-bootsplash/android/build/generated/source/codegen/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-bootsplash/android/build/generated/source/codegen/jni/react/renderer/components/RNBootSplashSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNBootSplashSpec_autolinked_build\\CMakeFiles\\react_codegen_RNBootSplashSpec.dir\\react\\renderer\\components\\RNBootSplashSpec\\ShadowNodes.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-bootsplash\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNBootSplashSpec\\ShadowNodes.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-bootsplash\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNBootSplashSpec\\ShadowNodes.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-bootsplash/android/build/generated/source/codegen/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-bootsplash/android/build/generated/source/codegen/jni/react/renderer/components/RNBootSplashSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNBootSplashSpec_autolinked_build\\CMakeFiles\\react_codegen_RNBootSplashSpec.dir\\react\\renderer\\components\\RNBootSplashSpec\\States.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-bootsplash\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNBootSplashSpec\\States.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-bootsplash\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNBootSplashSpec\\States.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\bac033cd950586cef66695376748dd33\\ComponentDescriptors.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\ComponentDescriptors.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\ComponentDescriptors.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\react\\renderer\\components\\rngesturehandler_codegen\\EventEmitters.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\EventEmitters.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\EventEmitters.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\react\\renderer\\components\\rngesturehandler_codegen\\Props.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\Props.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\Props.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\react\\renderer\\components\\rngesturehandler_codegen\\ShadowNodes.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\ShadowNodes.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\ShadowNodes.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\react\\renderer\\components\\rngesturehandler_codegen\\States.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\States.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\States.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\bac033cd950586cef66695376748dd33\\rngesturehandler_codegenJSI-generated.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\rngesturehandler_codegenJSI-generated.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\rngesturehandler_codegenJSI-generated.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\rngesturehandler_codegen-generated.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\rngesturehandler_codegen-generated.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\rngesturehandler_codegen-generated.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/react/renderer/components/RNImagePickerSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNImagePickerSpec_autolinked_build\\CMakeFiles\\react_codegen_RNImagePickerSpec.dir\\RNImagePickerSpec-generated.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-image-picker\\android\\build\\generated\\source\\codegen\\jni\\RNImagePickerSpec-generated.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-image-picker\\android\\build\\generated\\source\\codegen\\jni\\RNImagePickerSpec-generated.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/react/renderer/components/RNImagePickerSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNImagePickerSpec_autolinked_build\\CMakeFiles\\react_codegen_RNImagePickerSpec.dir\\react\\renderer\\components\\RNImagePickerSpec\\ComponentDescriptors.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-image-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNImagePickerSpec\\ComponentDescriptors.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-image-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNImagePickerSpec\\ComponentDescriptors.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/react/renderer/components/RNImagePickerSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNImagePickerSpec_autolinked_build\\CMakeFiles\\react_codegen_RNImagePickerSpec.dir\\react\\renderer\\components\\RNImagePickerSpec\\EventEmitters.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-image-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNImagePickerSpec\\EventEmitters.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-image-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNImagePickerSpec\\EventEmitters.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/react/renderer/components/RNImagePickerSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNImagePickerSpec_autolinked_build\\CMakeFiles\\react_codegen_RNImagePickerSpec.dir\\react\\renderer\\components\\RNImagePickerSpec\\Props.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-image-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNImagePickerSpec\\Props.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-image-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNImagePickerSpec\\Props.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/react/renderer/components/RNImagePickerSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNImagePickerSpec_autolinked_build\\CMakeFiles\\react_codegen_RNImagePickerSpec.dir\\react\\renderer\\components\\RNImagePickerSpec\\RNImagePickerSpecJSI-generated.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-image-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNImagePickerSpec\\RNImagePickerSpecJSI-generated.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-image-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNImagePickerSpec\\RNImagePickerSpecJSI-generated.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/react/renderer/components/RNImagePickerSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNImagePickerSpec_autolinked_build\\CMakeFiles\\react_codegen_RNImagePickerSpec.dir\\react\\renderer\\components\\RNImagePickerSpec\\ShadowNodes.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-image-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNImagePickerSpec\\ShadowNodes.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-image-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNImagePickerSpec\\ShadowNodes.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-image-picker/android/build/generated/source/codegen/jni/react/renderer/components/RNImagePickerSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNImagePickerSpec_autolinked_build\\CMakeFiles\\react_codegen_RNImagePickerSpec.dir\\react\\renderer\\components\\RNImagePickerSpec\\States.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-image-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNImagePickerSpec\\States.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-image-picker\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNImagePickerSpec\\States.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-nitro-modules/android/build/generated/source/codegen/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-nitro-modules/android/build/generated/source/codegen/jni/react/renderer/components/NitroModulesSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o NitroModulesSpec_autolinked_build\\CMakeFiles\\react_codegen_NitroModulesSpec.dir\\NitroModulesSpec-generated.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-nitro-modules\\android\\build\\generated\\source\\codegen\\jni\\NitroModulesSpec-generated.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-nitro-modules\\android\\build\\generated\\source\\codegen\\jni\\NitroModulesSpec-generated.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-nitro-modules/android/build/generated/source/codegen/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-nitro-modules/android/build/generated/source/codegen/jni/react/renderer/components/NitroModulesSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o NitroModulesSpec_autolinked_build\\CMakeFiles\\react_codegen_NitroModulesSpec.dir\\react\\renderer\\components\\NitroModulesSpec\\ComponentDescriptors.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-nitro-modules\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\NitroModulesSpec\\ComponentDescriptors.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-nitro-modules\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\NitroModulesSpec\\ComponentDescriptors.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-nitro-modules/android/build/generated/source/codegen/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-nitro-modules/android/build/generated/source/codegen/jni/react/renderer/components/NitroModulesSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o NitroModulesSpec_autolinked_build\\CMakeFiles\\react_codegen_NitroModulesSpec.dir\\react\\renderer\\components\\NitroModulesSpec\\EventEmitters.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-nitro-modules\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\NitroModulesSpec\\EventEmitters.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-nitro-modules\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\NitroModulesSpec\\EventEmitters.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-nitro-modules/android/build/generated/source/codegen/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-nitro-modules/android/build/generated/source/codegen/jni/react/renderer/components/NitroModulesSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o NitroModulesSpec_autolinked_build\\CMakeFiles\\react_codegen_NitroModulesSpec.dir\\react\\renderer\\components\\NitroModulesSpec\\NitroModulesSpecJSI-generated.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-nitro-modules\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\NitroModulesSpec\\NitroModulesSpecJSI-generated.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-nitro-modules\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\NitroModulesSpec\\NitroModulesSpecJSI-generated.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-nitro-modules/android/build/generated/source/codegen/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-nitro-modules/android/build/generated/source/codegen/jni/react/renderer/components/NitroModulesSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o NitroModulesSpec_autolinked_build\\CMakeFiles\\react_codegen_NitroModulesSpec.dir\\react\\renderer\\components\\NitroModulesSpec\\Props.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-nitro-modules\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\NitroModulesSpec\\Props.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-nitro-modules\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\NitroModulesSpec\\Props.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-nitro-modules/android/build/generated/source/codegen/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-nitro-modules/android/build/generated/source/codegen/jni/react/renderer/components/NitroModulesSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o NitroModulesSpec_autolinked_build\\CMakeFiles\\react_codegen_NitroModulesSpec.dir\\react\\renderer\\components\\NitroModulesSpec\\ShadowNodes.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-nitro-modules\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\NitroModulesSpec\\ShadowNodes.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-nitro-modules\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\NitroModulesSpec\\ShadowNodes.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-nitro-modules/android/build/generated/source/codegen/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-nitro-modules/android/build/generated/source/codegen/jni/react/renderer/components/NitroModulesSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o NitroModulesSpec_autolinked_build\\CMakeFiles\\react_codegen_NitroModulesSpec.dir\\react\\renderer\\components\\NitroModulesSpec\\States.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-nitro-modules\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\NitroModulesSpec\\States.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-nitro-modules\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\NitroModulesSpec\\States.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\04ea4e79765c200940068f01124b70f2\\components\\safeareacontext\\RNCSafeAreaViewShadowNode.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewShadowNode.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewShadowNode.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\04ea4e79765c200940068f01124b70f2\\components\\safeareacontext\\RNCSafeAreaViewState.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewState.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewState.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\8d2fa7b5b3f945cb81c02f9618f1a14a\\components\\safeareacontext\\ComponentDescriptors.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ComponentDescriptors.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ComponentDescriptors.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\74368587f2fc28b90e369b2eb499dec3\\react\\renderer\\components\\safeareacontext\\EventEmitters.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\EventEmitters.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\EventEmitters.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\ecf1dc5c1aae2045659408d017f4f27c\\jni\\react\\renderer\\components\\safeareacontext\\Props.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\Props.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\Props.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\74368587f2fc28b90e369b2eb499dec3\\react\\renderer\\components\\safeareacontext\\ShadowNodes.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ShadowNodes.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ShadowNodes.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\ecf1dc5c1aae2045659408d017f4f27c\\jni\\react\\renderer\\components\\safeareacontext\\States.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\States.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\States.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\8d2fa7b5b3f945cb81c02f9618f1a14a\\components\\safeareacontext\\safeareacontextJSI-generated.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\safeareacontextJSI-generated.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\safeareacontextJSI-generated.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\ac0e9d00c30b1c088c16bf1d0ce7e4a9\\generated\\source\\codegen\\jni\\safeareacontext-generated.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\safeareacontext-generated.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\safeareacontext-generated.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\45bd1f858b4262b72d828a91c003e4b5\\cpp\\react\\renderer\\components\\rnscreens\\RNSBottomTabsShadowNode.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSBottomTabsShadowNode.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSBottomTabsShadowNode.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\6cb789660870b4aba00f036a29b4ac85\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSBottomTabsState.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSBottomTabsState.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSBottomTabsState.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\ee13f0a865ad9811136982984b264fba\\react\\renderer\\components\\rnscreens\\RNSFullWindowOverlayShadowNode.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSFullWindowOverlayShadowNode.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSFullWindowOverlayShadowNode.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\45bd1f858b4262b72d828a91c003e4b5\\cpp\\react\\renderer\\components\\rnscreens\\RNSModalScreenShadowNode.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSModalScreenShadowNode.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSModalScreenShadowNode.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\6cb789660870b4aba00f036a29b4ac85\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenShadowNode.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenShadowNode.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenShadowNode.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\f19113d12c38634a97e5ade99105dc1b\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigShadowNode.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigShadowNode.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigShadowNode.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\ee13f0a865ad9811136982984b264fba\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigState.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigState.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigState.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\f19113d12c38634a97e5ade99105dc1b\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewShadowNode.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewShadowNode.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewShadowNode.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\f19113d12c38634a97e5ade99105dc1b\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewState.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewState.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewState.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\6cb789660870b4aba00f036a29b4ac85\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenState.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenState.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenState.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\ee13f0a865ad9811136982984b264fba\\react\\renderer\\components\\rnscreens\\RNSSplitViewScreenShadowNode.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSSplitViewScreenShadowNode.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSSplitViewScreenShadowNode.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\rnscreens.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-screens\\android\\src\\main\\jni\\rnscreens.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-screens\\android\\src\\main\\jni\\rnscreens.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\8ba71317575270fdd27186803e18456f\\jni\\react\\renderer\\components\\rnscreens\\ComponentDescriptors.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ComponentDescriptors.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ComponentDescriptors.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\2efe307802a9379cf1b66890012fb4dc\\codegen\\jni\\react\\renderer\\components\\rnscreens\\EventEmitters.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\EventEmitters.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\EventEmitters.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\7f3e7249e8fa2298f8602f2530aa0dfa\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\Props.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\Props.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\Props.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\7f3e7249e8fa2298f8602f2530aa0dfa\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ShadowNodes.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ShadowNodes.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ShadowNodes.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\7f3e7249e8fa2298f8602f2530aa0dfa\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\States.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\States.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\States.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\8ba71317575270fdd27186803e18456f\\jni\\react\\renderer\\components\\rnscreens\\rnscreensJSI-generated.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\rnscreensJSI-generated.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\rnscreensJSI-generated.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DREACT_NATIVE_MINOR_VERSION=79 -Dreact_codegen_rnsvg_EXPORTS -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\801af1ddc90ced64e392d0191b806f27\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGImageShadowNode.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-svg\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGImageShadowNode.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-svg\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGImageShadowNode.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DREACT_NATIVE_MINOR_VERSION=79 -Dreact_codegen_rnsvg_EXPORTS -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\d12b511f5572951da6c96ab77d0a8589\\react-native-svg\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGImageState.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-svg\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGImageState.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-svg\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGImageState.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DREACT_NATIVE_MINOR_VERSION=79 -Dreact_codegen_rnsvg_EXPORTS -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\801af1ddc90ced64e392d0191b806f27\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGLayoutableShadowNode.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-svg\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGLayoutableShadowNode.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-svg\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGLayoutableShadowNode.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DREACT_NATIVE_MINOR_VERSION=79 -Dreact_codegen_rnsvg_EXPORTS -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\801af1ddc90ced64e392d0191b806f27\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGShadowNodes.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-svg\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGShadowNodes.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-svg\\common\\cpp\\react\\renderer\\components\\rnsvg\\RNSVGShadowNodes.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DREACT_NATIVE_MINOR_VERSION=79 -Dreact_codegen_rnsvg_EXPORTS -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\rnsvg.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-svg\\android\\src\\main\\jni\\rnsvg.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-svg\\android\\src\\main\\jni\\rnsvg.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DREACT_NATIVE_MINOR_VERSION=79 -Dreact_codegen_rnsvg_EXPORTS -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\65187823d921ee3a128f4944e47c38c7\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\ComponentDescriptors.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\ComponentDescriptors.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\ComponentDescriptors.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DREACT_NATIVE_MINOR_VERSION=79 -Dreact_codegen_rnsvg_EXPORTS -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\c8d3a725c2bc1881474750c1ed918ef7\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\EventEmitters.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\EventEmitters.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\EventEmitters.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DREACT_NATIVE_MINOR_VERSION=79 -Dreact_codegen_rnsvg_EXPORTS -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\ca0cea3ac9a9dd5be29cbc4af99d5ed2\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\Props.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\Props.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\Props.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DREACT_NATIVE_MINOR_VERSION=79 -Dreact_codegen_rnsvg_EXPORTS -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\c8d3a725c2bc1881474750c1ed918ef7\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\ShadowNodes.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\ShadowNodes.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\ShadowNodes.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DREACT_NATIVE_MINOR_VERSION=79 -Dreact_codegen_rnsvg_EXPORTS -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\ca0cea3ac9a9dd5be29cbc4af99d5ed2\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\States.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\States.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\States.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -DREACT_NATIVE_MINOR_VERSION=79 -Dreact_codegen_rnsvg_EXPORTS -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnsvg_autolinked_build\\CMakeFiles\\react_codegen_rnsvg.dir\\65187823d921ee3a128f4944e47c38c7\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\rnsvgJSI-generated.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\rnsvgJSI-generated.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-svg\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnsvg\\rnsvgJSI-generated.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\RNVectorIconsSpec-generated.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\RNVectorIconsSpec-generated.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\RNVectorIconsSpec-generated.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\react\\renderer\\components\\RNVectorIconsSpec\\ComponentDescriptors.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\ComponentDescriptors.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\ComponentDescriptors.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\react\\renderer\\components\\RNVectorIconsSpec\\EventEmitters.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\EventEmitters.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\EventEmitters.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\react\\renderer\\components\\RNVectorIconsSpec\\Props.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\Props.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\Props.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\react\\renderer\\components\\RNVectorIconsSpec\\RNVectorIconsSpecJSI-generated.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\RNVectorIconsSpecJSI-generated.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\RNVectorIconsSpecJSI-generated.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\react\\renderer\\components\\RNVectorIconsSpec\\ShadowNodes.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\ShadowNodes.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\ShadowNodes.cpp"}, {"directory": "C:/chatspot-messenger/apps/mobile/android/app/.cxx/Debug/696786h6/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/. -IC:/chatspot-messenger/apps/mobile/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/react/renderer/components/RNVectorIconsSpec -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/b1a3b6ec2dcebbae5143874a82b244c4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.14.2/transforms/21a372fb7c68dfc7caec65d3b6f233d8/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNVectorIconsSpec_autolinked_build\\CMakeFiles\\react_codegen_RNVectorIconsSpec.dir\\react\\renderer\\components\\RNVectorIconsSpec\\States.cpp.o -c C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\States.cpp", "file": "C:\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNVectorIconsSpec\\States.cpp"}]