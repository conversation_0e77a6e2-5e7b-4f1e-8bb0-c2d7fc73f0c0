import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
} from 'react-native';
import Modal from 'react-native-modal';
import { useAppSelector } from '../hooks/redux';
import { selectAuthUser } from '../redux/slices/authSlice';
import { useTheme, radius } from '../theme';

interface NewChatModalProps {
  isVisible: boolean;
  onClose: () => void;
  onStartChat: (username: string) => void;
}

const NewChatModal: React.FC<NewChatModalProps> = ({
  isVisible,
  onClose,
  onStartChat,
}) => {
  const [username, setUsername] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const currentUser = useAppSelector(selectAuthUser);
  const { colors } = useTheme();

  const handleSubmit = async () => {
    const trimmedUsername = username.trim();

    if (!trimmedUsername) {
      Alert.alert('Error', 'Please enter a username');
      return;
    }

    // Check if user is trying to chat with themselves
    if (trimmedUsername.toLowerCase() === currentUser?.toLowerCase()) {
      Alert.alert('Error', 'You cannot start a chat with yourself');
      return;
    }

    try {
      setLoading(true);
      onStartChat(trimmedUsername);
      setUsername(''); // Clear input after successful submission
    } catch (error) {
      Alert.alert('Error', 'Failed to start chat');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setUsername(''); // Clear input when closing
    onClose();
  };

  const styles = createStyles(colors);

  return (
    <Modal
      isVisible={isVisible}
      onBackdropPress={handleClose}
      onBackButtonPress={handleClose}
      animationIn="slideInUp"
      animationOut="slideOutDown"
      backdropOpacity={0.5}
      style={styles.modal}
    >
      <View style={styles.modalContent}>
        <View style={styles.modalHeader}>
          <Text style={styles.modalTitle}>Start New Chat</Text>
          <TouchableOpacity onPress={handleClose} style={styles.closeButton}>
            <Text style={styles.closeButtonText}>×</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.formContainer}>
          <Text style={styles.label}>Enter Username:</Text>
          <TextInput
            style={styles.textInput}
            value={username}
            onChangeText={setUsername}
            placeholder="Enter the username to chat with"
            autoFocus
            editable={!loading}
            autoCapitalize="none"
            autoCorrect={false}
          />
        </View>

        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={[styles.button, styles.cancelButton]}
            onPress={handleClose}
            disabled={loading}
          >
            <Text style={styles.cancelButtonText}>Cancel</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.button, styles.startButton, loading && styles.disabledButton]}
            onPress={handleSubmit}
            disabled={loading}
          >
            <Text style={styles.startButtonText}>
              {loading ? 'Starting...' : 'Start Chat'}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  modal: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    margin:30,
  },
  modalContent: {
    backgroundColor: colors.cardBackgroundDS,
    borderRadius: 20,
    width: '100%',
    maxWidth: 400,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
  },
  modalTitle: {
    fontSize: 16,
    fontFamily: 'Outfit-Bold',
    color: colors.toneDark2,
  },
  closeButton: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: colors.gray100,
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonText: {
    fontSize: 20,
    color: colors.textSecondary,
    fontFamily: 'Outfit-Bold',
  },
  formContainer: {
    paddingHorizontal: 20,
    paddingBottom:20,
  },
  label: {
    fontSize: 14,
    fontFamily: 'Outfit-Medium',
    color: colors.toneDark1,
    marginBottom: 10,
  },
  textInput: {
    borderWidth: 1,
    borderColor: colors.toneLight2,
    borderRadius: radius.round,
    padding: 15,
    fontSize: 14,
    fontFamily: 'Outfit-Regular',
    color: colors.primary,
  },
  buttonContainer: {
    flexDirection: 'row',
    padding: 20,
    paddingTop: 0,
    gap: 10,
  },
  button: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: radius.round,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 6,
  },
  
  cancelButton: {
    backgroundColor: colors.gray100,
  },
  cancelButtonText: {
    fontSize: 14,
    fontFamily: 'Outfit-Medium',
    color: colors.textSecondary,
  },
  startButton: {
    backgroundColor: colors.toneDark2,
  },
  startButtonText: {
    fontSize: 14,
    fontFamily: 'Outfit-Medium',
    color: colors.white,
  },
  disabledButton: {
    backgroundColor: colors.gray300,
  },
});

export default NewChatModal;
