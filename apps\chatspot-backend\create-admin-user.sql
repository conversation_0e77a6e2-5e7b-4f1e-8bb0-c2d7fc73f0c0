-- Create an admin user if no admin exists
DO $$
DECLARE
    admin_exists BOOLEAN;
    hashed_password TEXT;
BEGIN
    -- Check if any admin user exists
    SELECT EXISTS(SELECT 1 FROM users WHERE is_admin = true) INTO admin_exists;
    
    -- If no admin exists, create one
    IF NOT admin_exists THEN
        -- Password: Admin@123 (pre-hashed with bcrypt)
        hashed_password := '$2b$10$XFE/oW.Lz7SrGZF1sB/Wne6HJ9cSRWjpd8GBVgdAUQqgWpRNfuS8a';
        
        INSERT INTO users (username, password, is_admin)
        VALUES ('admin', hashed_password, true);
        
        RAISE NOTICE 'Admin user created with username: admin and password: Admin@123';
    ELSE
        RAISE NOTICE 'Admin user already exists. No new admin created.';
    END IF;
END $$;
