import { DataSource } from 'typeorm';
import { User } from './src/auth/user.entity';
import { RefreshToken } from './src/auth/refresh-token.entity';
import { Message } from './src/chat/message.entity';
import { FcmToken } from './src/notifications/entities/fcm-token.entity';
import { Media } from './src/media/media.entity';

const isProduction = process.env.NODE_ENV === 'production';

const AppDataSource = new DataSource({
  type: 'postgres',
  url: process.env.DATABASE_URL,
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT ? parseInt(process.env.DB_PORT, 10) : 5432,
  username: process.env.DB_USERNAME || 'chatuser',
  password: process.env.DB_PASSWORD || 'chatpassword',
  database: process.env.DB_DATABASE || 'chatdb',
  synchronize: false,
  ssl: isProduction ? { rejectUnauthorized: false } : false,
  logging: true,
  entities: [User, RefreshToken, Message, FcmToken, Media],
  migrations: ['src/migration/**/*.ts'],
  migrationsTransactionMode: 'each',
});

export default AppDataSource;