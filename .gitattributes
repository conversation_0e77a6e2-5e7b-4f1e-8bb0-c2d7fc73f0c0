# Set default behavior to automatically normalize line endings
* text=auto

# Explicitly declare text files to be normalized
*.js text
*.jsx text
*.ts text
*.tsx text
*.json text
*.md text
*.css text
*.scss text
*.html text
*.svg text
*.yml text
*.yaml text

# Declare files that will always have CRLF line endings on checkout
*.bat text eol=crlf

# Declare files that will always have LF line endings on checkout
*.sh text eol=lf

# Denote all files that are truly binary and should not be modified
*.png binary
*.jpg binary
*.jpeg binary
*.gif binary
*.ico binary
*.woff binary
*.woff2 binary
*.ttf binary
*.eot binary
*.mp4 binary
*.webm binary
*.wav binary
*.mp3 binary
*.zip binary
*.pdf binary
