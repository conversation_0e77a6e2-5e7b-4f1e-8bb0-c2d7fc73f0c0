import {
  Controller,
  Post,
  Get,
  Delete,
  Param,
  UseGuards,
  Request,
  UseInterceptors,
  UploadedFile,
  Body,
  Query,
  BadRequestException,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiConsumes,
  ApiBody,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { MediaService } from './media.service';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { Media } from './media.entity';

@ApiTags('media')
@Controller('api/media')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class MediaController {
  constructor(private readonly mediaService: MediaService) {}

  @Post('upload')
  @ApiOperation({ summary: 'Upload a media file' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'Media file to upload',
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
      },
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Media uploaded successfully',
    type: Media,
  })
  @ApiResponse({ status: 400, description: 'Invalid file or file too large' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @UseInterceptors(FileInterceptor('file'))
  async uploadMedia(
    @UploadedFile() file: Express.Multer.File,
    @Request() req: { user: { username: string } },
  ): Promise<Media> {
    if (!file) {
      throw new BadRequestException('No file provided');
    }

    const username = req.user.username;
    return this.mediaService.uploadMedia(file, username);
  }

  @Get(':id/signed-url')
  @ApiOperation({ summary: 'Get signed URL for media file access' })
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: 'Media file ID',
  })
  @ApiQuery({
    name: 'expiresIn',
    type: 'number',
    required: false,
    description: 'Signed URL expiration in seconds (default: 3600)',
  })
  @ApiResponse({
    status: 200,
    description: 'Signed URL generated successfully',
    schema: {
      type: 'object',
      properties: {
        media: { $ref: '#/components/schemas/Media' },
        signedUrl: { type: 'string' },
        expiresAt: { type: 'string', format: 'date-time' },
      },
    },
  })
  @ApiResponse({ status: 404, description: 'Media not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async getMediaSignedUrl(
    @Param('id') id: string,
    @Query('expiresIn') expiresIn?: string,
  ): Promise<{
    media: Media;
    signedUrl: string;
    expiresAt: Date;
  }> {
    const expiresInSeconds = expiresIn ? parseInt(expiresIn, 10) : 3600; // Default 1 hour
    return this.mediaService.getMediaSignedUrl(id, expiresInSeconds);
  }

  @Get(':id/info')
  @ApiOperation({ summary: 'Get media information' })
  @ApiResponse({
    status: 200,
    description: 'Media information retrieved successfully',
    type: Media,
  })
  @ApiResponse({ status: 404, description: 'Media not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async getMediaInfo(@Param('id') id: string): Promise<Media> {
    return this.mediaService.getMedia(id);
  }

  @Delete(':id')
  @ApiOperation({ summary: 'Delete a media file' })
  @ApiParam({
    name: 'id',
    type: 'string',
    required: true,
    description: 'Media file ID',
  })
  @ApiResponse({
    status: 200,
    description: 'Media deleted successfully',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string' },
      },
    },
  })
  @ApiResponse({ status: 404, description: 'Media not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - not the owner' })
  async deleteMedia(
    @Param('id') id: string,
    @Request() req: { user: { username: string } },
  ): Promise<{ message: string }> {
    const username = req.user.username;
    await this.mediaService.deleteMedia(id, username);
    return { message: 'Media deleted successfully' };
  }
}
