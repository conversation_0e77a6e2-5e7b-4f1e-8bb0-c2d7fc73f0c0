# Media Upload Feature for Mobile App

## Overview
This document describes the media upload functionality implemented in the mobile app, allowing users to send images, documents, and other media files in their chat messages.

## Features

### Supported Media Types
- **Images**: JPEG, JPG, PNG, GIF, WebP
- **Documents**: PDF, Word documents, Excel spreadsheets, PowerPoint presentations
- **Text files**: Plain text files
- **Other**: Any file type supported by the device's document picker

### Media Upload Options
1. **Camera**: Take a new photo using the device camera
2. **Gallery**: Select an existing image from the device gallery
3. **Document**: Pick any file from the device storage

### File Size Limits
- Maximum file size: 10MB
- Images are automatically compressed to 1024x1024 pixels with 80% quality

## Implementation Details

### Components Added/Modified

#### 1. Media Upload Service (`src/services/mediaUploadService.ts`)
- Handles image picking from gallery
- Handles camera photo capture
- Handles document picking
- Provides file type validation
- Provides file size formatting utilities
- Manages temporary file cleanup

#### 2. Database Schema Updates (`src/database/schema.ts`)
- Added media support fields to the chats table:
  - `media_uri`: File URI/path
  - `media_name`: Original filename
  - `media_type`: MIME type
  - `media_size`: File size in bytes

#### 3. Chat Model Updates (`src/database/models/Chat.ts`)
- Added media fields to the Chat model
- Updated MessageType to include 'media'
- Updated toJSON method to include media data

#### 4. Database Service Updates (`src/database/service.ts`)
- Added `saveMediaMessage` method for storing media messages
- Handles room updates with media message previews

#### 5. Redux Updates
- **Socket Slice** (`src/redux/slices/socketSlice.ts`):
  - Added 'media' to MessageType
  - Added mediaData to SendMessageRequestPayload
- **Socket Saga** (`src/redux/sagas/socketSaga.ts`):
  - Added media message handling for sending and receiving
  - Includes acknowledgment callbacks for media messages

#### 6. UI Components
- **MessageInput** (`src/components/MessageInput.tsx`):
  - Added media upload button (attachment icon)
  - Added media upload handler with file validation
  - Shows upload status messages
- **MediaMessageItem** (`src/components/MediaMessageItem.tsx`):
  - New component for displaying media messages
  - Handles image display with loading states
  - Handles document display with file icons
  - Supports tap to open/view media
- **MessageItem** (`src/components/MessageItem.tsx`):
  - Updated to route media messages to MediaMessageItem component

### Dependencies Added
```json
{
  "react-native-image-picker": "^8.2.1",
  "react-native-document-picker": "^9.3.1",
  "react-native-fs": "^2.20.0"
}
```

## Usage

### For Users
1. Open a chat conversation
2. Tap the attachment icon (📎) next to the text input
3. Choose from:
   - **Camera**: Take a new photo
   - **Gallery**: Select an existing image
   - **Document**: Pick any file
4. The media will be sent immediately (no caption support yet)

### For Developers

#### Sending Media Messages
```typescript
import { mediaUploadService } from '../services/mediaUploadService';

// Show media options and handle selection
const result = await mediaUploadService.showMediaOptions();
if (result.success && result.file) {
  dispatch(sendMessageRequest({
    receiverUsername: 'user123',
    messageText: '', // Optional caption
    messageType: 'media',
    mediaData: result.file
  }));
}
```

#### Handling Media Messages in UI
```typescript
// Media messages are automatically routed to MediaMessageItem
if (message.type === 'media') {
  return <MediaMessageItem message={message} />;
}
```

## File Storage

### Local Storage
- Images and documents are stored locally using their original URIs
- Temporary files (from document picker) are cleaned up automatically
- Media files are not automatically downloaded to device storage

### Database Storage
- Media metadata is stored in the local WatermelonDB
- File URIs, names, types, and sizes are preserved
- Messages maintain their media associations

## Security & Privacy

### File Access
- Uses device-native pickers for secure file access
- Only accesses files explicitly selected by the user
- No background file scanning or access

### Data Handling
- File data is transmitted as base64 for images
- Document URIs are shared between devices
- No server-side file storage (peer-to-peer sharing)

## Error Handling

### Common Error Scenarios
1. **File too large**: Shows "File size too large (max 10MB)" message
2. **Unsupported file type**: Shows "File type not supported" message
3. **Permission denied**: Handled by native pickers
4. **Network issues**: Shows connection error messages

### Error Recovery
- Failed uploads don't affect the chat state
- Users can retry uploads immediately
- No partial uploads or corrupted states

## Future Enhancements

### Planned Features
1. **Caption support**: Add text captions to media messages
2. **Multiple file selection**: Send multiple files at once
3. **File preview**: Preview files before sending
4. **Download management**: Save files to device storage
5. **Full-screen image viewer**: Better image viewing experience
6. **Video support**: Add video file support
7. **Audio support**: Add audio file support

### Technical Improvements
1. **Progressive uploads**: Show upload progress
2. **Compression options**: User-selectable image quality
3. **File caching**: Cache frequently accessed files
4. **Offline support**: Queue uploads when offline

## Troubleshooting

### Common Issues
1. **Media not displaying**: Check file URI validity and permissions
2. **Upload failures**: Verify network connection and file size
3. **Permission errors**: Ensure app has necessary permissions
4. **Memory issues**: Large files may cause memory pressure

### Debug Information
- Check console logs for detailed error messages
- Verify file URIs in database records
- Monitor network requests for media data

## Testing

### Manual Testing Checklist
- [ ] Camera photo capture
- [ ] Gallery image selection
- [ ] Document file selection
- [ ] File size validation
- [ ] File type validation
- [ ] Media message display
- [ ] Image loading states
- [ ] Document icon display
- [ ] Error message display
- [ ] Network error handling

### Automated Testing
- Unit tests for media service functions
- Integration tests for database operations
- UI tests for media message components
- Network tests for upload/download scenarios 