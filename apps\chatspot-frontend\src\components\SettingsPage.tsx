import { useNavigate } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { selectUser, logout } from '../redux/slices/authSlice';
import Settings from './Settings';
import './Chat.css';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'; // <-- This is important!
import { faCoffee } from '@fortawesome/free-solid-svg-icons';  // <-- Import the icons you need
import { faAngleLeft } from '@fortawesome/free-solid-svg-icons';

const SettingsPage: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const currentUser = useSelector(selectUser);

  const handleClose = () => {
    navigate('/chat');
  };

  const handleLogout = () => {
    dispatch(logout());
    navigate('/login');
  };

  return (
    <div className="chat-container">
      <div className="chat-content">
        <div className="messages-section" style={{ flex: 1 }}>
          <div className="mobile-nav-controls active">
            <button className="back-button" onClick={handleClose}>
              <span className="back-button-icon"><FontAwesomeIcon icon={faAngleLeft} size="xl"  /></span> Back to Chats
            </button>
          </div>
          <div className="chat-window-container">
            <Settings
              onClose={handleClose}
              onLogout={handleLogout}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default SettingsPage;
