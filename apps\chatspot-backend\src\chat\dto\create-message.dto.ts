import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional } from 'class-validator';

export class CreateMessageDto {
  @ApiProperty({
    description: 'Username of the sender',
    example: 'johndoe',
  })
  @IsString()
  @IsNotEmpty()
  sender_username: string;

  @ApiProperty({
    description: 'Username of the receiver',
    example: 'janedoe',
  })
  @IsString()
  @IsNotEmpty()
  receiver_username: string;

  @ApiProperty({
    description: 'Message content',
    example: 'Hello, how are you?',
  })
  @IsString()
  @IsNotEmpty()
  message: string;

  @ApiProperty({
    description: 'Message type',
    example: 'text',
    enum: ['text', 'media', 'clear_chat', 'typing', 'delete_user', 'system'],
    default: 'text',
  })
  @IsString()
  @IsOptional()
  type?: 'text' | 'media' | 'clear_chat' | 'typing' | 'delete_user' | 'system';

  @ApiProperty({
    description: 'Media ID for media messages',
    example: '123e4567-e89b-12d3-a456-426614174000',
    required: false,
  })
  @IsString()
  @IsOptional()
  media_id?: string;

  @ApiProperty({
    description: 'Media URL for media messages',
    example: '/api/media/123e4567-e89b-12d3-a456-426614174000',
    required: false,
  })
  @IsString()
  @IsOptional()
  media_url?: string;

  @ApiProperty({
    description: 'Media type (image, video, audio, document)',
    example: 'image',
    required: false,
  })
  @IsString()
  @IsOptional()
  media_type?: string;

  @ApiProperty({
    description: 'Original filename of the media',
    example: 'vacation_photo.jpg',
    required: false,
  })
  @IsString()
  @IsOptional()
  media_filename?: string;

  @ApiProperty({
    description: 'File size in bytes',
    example: 2048576,
    required: false,
  })
  @IsOptional()
  media_file_size?: number;

  @ApiProperty({
    description: 'Client-generated message ID for tracking',
    example: 'client-msg-123',
    required: false,
  })
  @IsString()
  @IsOptional()
  client_message_id?: string;
}
