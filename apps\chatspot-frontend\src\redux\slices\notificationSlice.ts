import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { RootState } from '../store';

// Define the notification state interface
interface NotificationState {
  permissionGranted: boolean;
  fcmToken: string | null;
  isInitialized: boolean;
}

// Initial state
const initialState: NotificationState = {
  permissionGranted: false,
  fcmToken: null,
  isInitialized: false,
};

// Create the notification slice
const notificationSlice = createSlice({
  name: 'notification',
  initialState,
  reducers: {
    // Set permission status
    setPermissionStatus: (state, action: PayloadAction<boolean>) => {
      state.permissionGranted = action.payload;
    },

    // Set FCM token
    setFcmToken: (state, action: PayloadAction<string | null>) => {
      state.fcmToken = action.payload;
    },

    // Set initialization status
    setInitialized: (state, action: PayloadAction<boolean>) => {
      state.isInitialized = action.payload;
    },
  },
});

// Export actions
export const {
  setPermissionStatus,
  setFcmToken,
  setInitialized,
} = notificationSlice.actions;

// Export selectors
export const selectPermissionGranted = (state: RootState) => state.notification.permissionGranted;
export const selectFcmToken = (state: RootState) => state.notification.fcmToken;
export const selectIsInitialized = (state: RootState) => state.notification.isInitialized;

// Export reducer
export default notificationSlice.reducer;
