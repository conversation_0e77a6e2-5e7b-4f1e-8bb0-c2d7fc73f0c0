import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  ActivityIndicator,
} from 'react-native';
import Modal from 'react-native-modal';
import { useTheme, radius } from '../theme';

interface RecallLimitModalProps {
  isVisible: boolean;
  selectedLimit: number;
  onClose: () => void;
  onConfirm: (value: number) => void;
  loading: boolean;
}

const RecallLimitModal: React.FC<RecallLimitModalProps> = ({
  isVisible,
  selectedLimit,
  onClose,
  onConfirm,
  loading,
}) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);

  const [limit, setLimit] = useState<number>(selectedLimit);

  const options = [0, ...Array.from({ length: 12 }, (_, i) => i + 1)];

  return (
    <Modal
      isVisible={isVisible}
      onBackdropPress={onClose}
      onBackButtonPress={onClose}
      animationIn="fadeIn"
      animationOut="fadeOut"
      backdropOpacity={0.5}
    >
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          <Text style={styles.title}>Set Recall Limit</Text>
          <Text style={styles.message}>
            Choose how many messages can't be recalled. Select "Off" to disable message recall.
          </Text>

          <ScrollView
            style={styles.optionList}
            contentContainerStyle={{ paddingVertical: 4 }}
            showsVerticalScrollIndicator={false}
          >
            {options.map((value) => (
              <TouchableOpacity
                key={value}
                style={[
                  styles.optionButton,
                  limit === value && styles.optionButtonSelected,
                ]}
                onPress={() => setLimit(value)}
                disabled={loading}
              >
                <Text
                  style={[
                    styles.optionText,
                    limit === value && styles.optionTextSelected,
                  ]}
                >
                  {value === 0 ? 'Off' : value}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>

          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={[styles.button, styles.cancelButton]}
              onPress={onClose}
              disabled={loading}
            >
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.button, styles.confirmButton]}
              onPress={() => onConfirm(limit)}
              disabled={loading}
            >
              {loading ? (
                <ActivityIndicator size="small" color="#ffffff" />
              ) : (
                <Text style={styles.confirmButtonText}>Save</Text>
              )}
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const createStyles = (colors: any) =>
  StyleSheet.create({
    modalContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      margin: 30,
    },
    modalContent: {
      backgroundColor: colors.cardBackground,
      borderRadius: 20,
      padding: 24,
      width: '90%',
      maxWidth: 400,
      maxHeight: 500,
    },
    title: {
      fontSize: 16,
      fontFamily: 'Outfit-Bold',
      color: colors.text,
      marginBottom: 12,
      textAlign: 'center',
    },
    message: {
      fontSize: 14,
      fontFamily: 'Outfit-Regular',
      color: colors.textSecondary,
      marginBottom: 16,
      textAlign: 'center',
    },
    optionList: {
      maxHeight: 200,
      marginBottom: 24,
    },
    optionButton: {
      paddingVertical: 10,
      paddingHorizontal: 12,
      borderRadius: radius.round,
      backgroundColor: colors.surface,
      marginBottom: 8,
      alignItems: 'center',
    },
    optionButtonSelected: {
      backgroundColor: colors.primary,
    },
    optionText: {
      fontSize: 14,
      fontFamily: 'Outfit-Medium',
      color: colors.text,
    },
    optionTextSelected: {
      color: colors.white,
    },
    buttonContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
    },
    button: {
      flex: 1,
      paddingVertical: 12,
      borderRadius: radius.round,
      alignItems: 'center',
      justifyContent: 'center',
      marginHorizontal: 6,
    },
    cancelButton: {
      backgroundColor: colors.toneLight3,
    },
    confirmButton: {
      backgroundColor: colors.toneDark2,
    },
    cancelButtonText: {
      color: colors.text,
      fontSize: 14,
      fontFamily: 'Outfit-Medium',
    },
    confirmButtonText: {
      color: colors.white,
      fontSize: 14,
      fontFamily: 'Outfit-Medium',
    },
  });

export default RecallLimitModal;
