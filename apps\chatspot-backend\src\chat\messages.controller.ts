import { Controller, Get, UseGuards, Request, Param, Inject, forwardRef } from '@nestjs/common';
import { MessageService } from './message.service';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiParam } from '@nestjs/swagger';
import { MessageDto } from './dto/message.dto';
import { ChatGateway } from './chat.gateway';

@ApiTags('messages')
@Controller('api/messages')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth('JWT-auth')
export class MessagesController {
  constructor(
    private readonly messageService: MessageService,
    @Inject(forwardRef(() => ChatGateway))
    private readonly chatGateway: ChatGateway
  ) {}

  @Get()
  @ApiOperation({ summary: 'Get all messages for the authenticated user' })
  @ApiResponse({
    status: 200,
    description: 'Returns all messages for the user',
    type: [MessageDto],
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async getAllMessages(@Request() req: any): Promise<MessageDto[]> {
    const username = req.user.username;

    // Get all messages for the user
    const messages = await this.messageService.getAllMessagesForUsername(username);

    // Identify pending messages that this user is receiving
    const pendingMessagesForUser = messages.filter(
      msg => msg.receiver_username === username && msg.status === 'sent'
    );

    // Process delivered messages that this user sent (for delivery confirmations and cleanup)
    const deliveredMessagesSentByUser = messages.filter(
      msg => msg.sender_username === username && msg.status === 'delivered'
    );

    // Delete delivered messages sent by this user immediately since they're actively fetching them
    // This prevents the loophole where messages remain in the database after being delivered
    if (deliveredMessagesSentByUser.length > 0) {
      console.log(`Found ${deliveredMessagesSentByUser.length} delivered messages sent by ${username}`);

      try {
        const messageIds = deliveredMessagesSentByUser.map(msg => msg.id);
        console.log(`Deleting ${messageIds.length} delivered messages for sender ${username}: ${messageIds.join(', ')}`);

        for (const messageId of messageIds) {
          await this.messageService.delete(messageId);
        }

        console.log(`Successfully deleted ${messageIds.length} delivered messages for sender ${username}`);

        // Remove deleted messages from the response
        const deletedMessageIds = new Set(messageIds);
        const filteredMessages = messages.filter(msg => !deletedMessageIds.has(msg.id));

        // Mark pending messages as delivered AFTER preparing the response
        // This ensures the client gets the original status first
        this.markPendingMessagesAsDeliveredAsync(pendingMessagesForUser, username);

        return filteredMessages;
      } catch (error) {
        console.error(`Failed to delete delivered messages for sender ${username}:`, error);
      }
    }

    // Mark pending messages as delivered AFTER preparing the response
    // This ensures the client gets the original status first
    this.markPendingMessagesAsDeliveredAsync(pendingMessagesForUser, username);

    return messages;
  }

  @Get('conversation/:otherUsername')
  @ApiOperation({ summary: 'Get messages for a specific conversation' })
  @ApiParam({
    name: 'otherUsername',
    description: 'Username of the other participant in the conversation',
    example: 'johndoe'
  })
  @ApiResponse({
    status: 200,
    description: 'Returns messages for the conversation',
    type: [MessageDto],
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async getConversationMessages(
    @Request() req: any,
    @Param('otherUsername') otherUsername: string
  ): Promise<MessageDto[]> {
    const username = req.user.username;

    // Get messages for the conversation
    const messages = await this.messageService.getMessagesForConversation(username, otherUsername);

    // Identify pending messages that this user is receiving from the other user
    const pendingMessagesForUser = messages.filter(
      msg => msg.receiver_username === username &&
             msg.sender_username === otherUsername &&
             msg.status === 'sent'
    );

    // Mark pending messages as delivered AFTER preparing the response
    // This ensures the client gets the original status first
    if (pendingMessagesForUser.length > 0) {
      this.markPendingMessagesAsDeliveredAsync(pendingMessagesForUser, username);
    }

    return messages;
  }

  @Get('pending')
  @ApiOperation({ summary: 'Get pending messages for the authenticated user' })
  @ApiResponse({
    status: 200,
    description: 'Returns pending messages for the user',
    type: [MessageDto],
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async getPendingMessages(@Request() req: any): Promise<MessageDto[]> {
    const username = req.user.username;

    // Get pending messages for the user
    const messages = await this.messageService.getPendingMessagesForUsername(username);

    // Identify pending messages that this user is receiving
    const pendingMessagesForUser = messages.filter(
      msg => msg.receiver_username === username && msg.status === 'sent'
    );

    // Mark pending messages as delivered AFTER preparing the response
    // This ensures the client gets the original status first
    if (pendingMessagesForUser.length > 0) {
      this.markPendingMessagesAsDeliveredAsync(pendingMessagesForUser, username);
    }

    return messages;
  }

  /**
   * Asynchronously mark pending messages as delivered after the response has been sent
   * This ensures the client receives the original status first, then the database is updated
   */
  private markPendingMessagesAsDeliveredAsync(pendingMessages: MessageDto[], username: string): void {
    // Use setImmediate to ensure this runs after the response is sent
    setImmediate(async () => {
      for (const message of pendingMessages) {
        try {
          // Mark as delivered in database
          await this.messageService.markAsDelivered(message.id);

          // Notify sender about delivery via socket (if sender is online)
          const deliveryNotified = this.chatGateway.notifyMessageDelivered(
            message.sender_username,
            message.id,
            message.receiver_username,
            message.client_message_id
          );

          console.log(`Message ${message.id} marked as delivered for ${username}. Sender notified: ${deliveryNotified}`);
        } catch (error) {
          console.error(`Failed to mark message ${message.id} as delivered:`, error);
        }
      }
    });
  }

}
