import { mediaApiService } from './mediaApi';

export interface MediaFile {
  id: string;
  uri: string;
  name: string;
  type: string;
  size: number;
  signedUrl?: string;
}

export interface MediaUploadResult {
  success: boolean;
  file?: MediaFile;
  error?: string;
}

class MediaUploadService {
  /**
   * Create object URL for file preview
   */
  private createObjectURL(file: File): string {
    return URL.createObjectURL(file);
  }

  /**
   * Upload file to backend and get media info
   */
  private async uploadToBackend(file: File): Promise<MediaFile> {
    // Upload file to backend
    const uploadResponse = await mediaApiService.uploadMedia(file);

    // Get signed URL for access
    const signedUrlResponse = await mediaApiService.getMediaSignedUrl(uploadResponse.id);

    return {
      id: uploadResponse.id,
      uri: signedUrlResponse.signedUrl,
      name: uploadResponse.original_filename,
      type: uploadResponse.mime_type,
      size: uploadResponse.file_size,
      signedUrl: signedUrlResponse.signedUrl,
    };
  }

  /**
   * Pick an image from file input
   */
  async pickImage(): Promise<MediaUploadResult> {
    return new Promise((resolve) => {
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = 'image/*';
      input.multiple = false;

      input.onchange = async (event) => {
        const target = event.target as HTMLInputElement;
        const file = target.files?.[0];

        if (!file) {
          resolve({ success: false, error: 'No file selected' });
          return;
        }

        try {
          // Validate file type
          if (!this.isSupportedFileType(file.type)) {
            resolve({ success: false, error: 'File type not supported' });
            return;
          }

          // Validate file size (10MB limit)
          if (file.size > 10 * 1024 * 1024) {
            resolve({ success: false, error: 'File size too large (max 10MB)' });
            return;
          }

          // Upload to backend
          const mediaFile = await this.uploadToBackend(file);

          resolve({ success: true, file: mediaFile });
        } catch (error) {
          console.error('Error uploading image:', error);
          resolve({ success: false, error: error instanceof Error ? error.message : 'Failed to upload image' });
        }
      };

      input.oncancel = () => {
        resolve({ success: false, error: 'User cancelled file selection' });
      };

      input.click();
    });
  }

  /**
   * Pick a video from file input
   */
  async pickVideo(): Promise<MediaUploadResult> {
    return new Promise((resolve) => {
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = 'video/*';
      input.multiple = false;

      input.onchange = async (event) => {
        const target = event.target as HTMLInputElement;
        const file = target.files?.[0];

        if (!file) {
          resolve({ success: false, error: 'No file selected' });
          return;
        }

        try {
          // Validate file type
          if (!this.isSupportedFileType(file.type)) {
            resolve({ success: false, error: 'File type not supported' });
            return;
          }

          // Validate file size (50MB limit for videos)
          if (file.size > 50 * 1024 * 1024) {
            resolve({ success: false, error: 'File size too large (max 50MB for videos)' });
            return;
          }

          // Upload to backend
          const mediaFile = await this.uploadToBackend(file);

          resolve({ success: true, file: mediaFile });
        } catch (error) {
          console.error('Error uploading video:', error);
          resolve({ success: false, error: error instanceof Error ? error.message : 'Failed to upload video' });
        }
      };

      input.oncancel = () => {
        resolve({ success: false, error: 'User cancelled file selection' });
      };

      input.click();
    });
  }

  /**
   * Pick an audio file from file input
   */
  async pickAudio(): Promise<MediaUploadResult> {
    return new Promise((resolve) => {
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = 'audio/*';
      input.multiple = false;

      input.onchange = async (event) => {
        const target = event.target as HTMLInputElement;
        const file = target.files?.[0];

        if (!file) {
          resolve({ success: false, error: 'No file selected' });
          return;
        }

        try {
          // Validate file type
          if (!this.isSupportedFileType(file.type)) {
            resolve({ success: false, error: 'File type not supported' });
            return;
          }

          // Validate file size (25MB limit for audio)
          if (file.size > 25 * 1024 * 1024) {
            resolve({ success: false, error: 'File size too large (max 25MB for audio)' });
            return;
          }

          // Upload to backend
          const mediaFile = await this.uploadToBackend(file);

          resolve({ success: true, file: mediaFile });
        } catch (error) {
          console.error('Error uploading audio:', error);
          resolve({ success: false, error: error instanceof Error ? error.message : 'Failed to upload audio' });
        }
      };

      input.oncancel = () => {
        resolve({ success: false, error: 'User cancelled file selection' });
      };

      input.click();
    });
  }

  /**
   * Pick a document from file input
   */
  async pickDocument(): Promise<MediaUploadResult> {
    return new Promise((resolve) => {
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = '.pdf,.doc,.docx,.txt,.xls,.xlsx,.ppt,.pptx';
      input.multiple = false;

      input.onchange = async (event) => {
        const target = event.target as HTMLInputElement;
        const file = target.files?.[0];

        if (!file) {
          resolve({ success: false, error: 'No file selected' });
          return;
        }

        try {
          // Validate file type
          if (!this.isSupportedFileType(file.type)) {
            resolve({ success: false, error: 'File type not supported' });
            return;
          }

          // Validate file size (10MB limit)
          if (file.size > 10 * 1024 * 1024) {
            resolve({ success: false, error: 'File size too large (max 10MB)' });
            return;
          }

          // Upload to backend
          const mediaFile = await this.uploadToBackend(file);

          resolve({ success: true, file: mediaFile });
        } catch (error) {
          console.error('Error uploading document:', error);
          resolve({ success: false, error: error instanceof Error ? error.message : 'Failed to upload document' });
        }
      };

      input.oncancel = () => {
        resolve({ success: false, error: 'User cancelled file selection' });
      };

      input.click();
    });
  }

  /**
   * Show media selection options (deprecated - use modal directly)
   */
  async showMediaOptions(): Promise<MediaUploadResult> {
    // This method is deprecated - the modal is now shown directly from the MessageInput component
    return { success: false, error: 'Use modal directly instead' };
  }

  /**
   * Get file size in human readable format
   */
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Check if file type is supported
   */
  isSupportedFileType(type: string): boolean {
    const supportedTypes = [
      // Images
      'image/jpeg',
      'image/jpg',
      'image/png',
      'image/gif',
      'image/webp',
      // Videos
      'video/mp4',
      'video/mov',
      'video/avi',
      'video/quicktime',
      'video/3gpp',
      'video/webm',
      // Audio
      'audio/mp3',
      'audio/mpeg',
      'audio/wav',
      'audio/aac',
      'audio/m4a',
      'audio/ogg',
      'audio/webm',
      // Documents
      'application/pdf',
      'text/plain',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    ];

    return supportedTypes.includes(type.toLowerCase());
  }

  /**
   * Get file icon based on type
   */
  getFileIcon(type: string): string {
    if (type.startsWith('image/')) {
      return '🖼️';
    } else if (type.startsWith('video/')) {
      return '🎥';
    } else if (type.startsWith('audio/')) {
      return '🎵';
    } else if (type === 'application/pdf') {
      return '📄';
    } else if (type.includes('word') || type.includes('document')) {
      return '📝';
    } else if (type.includes('excel') || type.includes('spreadsheet')) {
      return '📊';
    } else if (type.includes('powerpoint') || type.includes('presentation')) {
      return '📈';
    } else if (type.startsWith('text/')) {
      return '📄';
    } else {
      return '📎';
    }
  }

  /**
   * Clean up object URLs
   */
  cleanupObjectURL(uri: string): void {
    if (uri.startsWith('blob:')) {
      URL.revokeObjectURL(uri);
    }
  }

  /**
   * Get signed URL for existing media
   */
  async getSignedUrl(mediaId: string): Promise<string> {
    const response = await mediaApiService.getMediaSignedUrl(mediaId);
    return response.signedUrl;
  }

  /**
   * Delete media from backend
   */
  async deleteMedia(mediaId: string): Promise<void> {
    await mediaApiService.deleteMedia(mediaId);
  }
}

export const mediaUploadService = new MediaUploadService();