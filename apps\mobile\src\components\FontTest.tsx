import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
} from 'react-native';
import { useTheme, typography } from '../theme';

const FontTest: React.FC = () => {
  const { colors } = useTheme();

  return (
    <ScrollView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.content}>
        <Text style={[typography.h1, { color: colors.text }]}>
          Outfit Font Test - H1 Bold
        </Text>
        
        <Text style={[typography.h2, { color: colors.text }]}>
          Outfit Font Test - H2 Bold
        </Text>
        
        <Text style={[typography.h3, { color: colors.text }]}>
          Outfit Font Test - H3 Medium
        </Text>
        
        <Text style={[typography.body, { color: colors.text }]}>
          This is body text using Outfit Regular font. It should look clean and modern, matching the frontend design.
        </Text>
        
        <Text style={[typography.bodySecondary, { color: colors.textSecondary }]}>
          This is secondary body text using Outfit Regular font with secondary color.
        </Text>
        
        <Text style={[typography.caption, { color: colors.textSecondary }]}>
          This is caption text using Outfit Regular font.
        </Text>
        
        <Text style={[typography.link, { color: colors.primary }]}>
          This is link text using Outfit Regular font.
        </Text>

        <View style={styles.fontWeightTest}>
          <Text style={[styles.testText, { fontFamily: 'Outfit-Regular', color: colors.text }]}>
            Outfit Regular (400)
          </Text>
          <Text style={[styles.testText, { fontFamily: 'Outfit-Medium', color: colors.text }]}>
            Outfit Medium (500)
          </Text>
          <Text style={[styles.testText, { fontFamily: 'Outfit-Bold', color: colors.text }]}>
            Outfit Bold (700)
          </Text>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    padding: 20,
    gap: 16,
  },
  fontWeightTest: {
    marginTop: 20,
    padding: 16,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    gap: 8,
  },
  testText: {
    fontSize: 16,
    lineHeight: 24,
  },
});

export default FontTest;
