import React from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  RefreshControl,
} from 'react-native';
import SwipeableRoomItem from './SwipeableRoomItem';
import { useTheme } from '../theme';
import Icon from 'react-native-vector-icons/MaterialIcons';

interface RoomsListProps {
  rooms: Record<string, any>[];
  onRoomSelect: (username: string) => void;
  selectedUsername?: string | null;
  onNewChat?: () => void;
  onClearChat: (username: string) => void;
  onDeleteUser: (username: string) => void;
  onRefresh?: () => Promise<void>;
  refreshing?: boolean;
}

const RoomsList: React.FC<RoomsListProps> = ({
  rooms,
  onRoomSelect,
  selectedUsername,
  onNewChat,
  onClearChat,
  onDeleteUser,
  onRefresh,
  refreshing = false,
}) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);

  const renderRoom = ({ item }: { item: Record<string, any> }) => (
    <SwipeableRoomItem
      room={item}
      onPress={onRoomSelect}
      onClearChat={onClearChat}
      onDeleteUser={onDeleteUser}
      isSelected={item.username === selectedUsername}
    />
  );

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Text style={styles.emptyIcon}><Icon name="messenger" size={30} color={colors.toneLight2} /></Text>
      <Text style={styles.emptyTitle}>No conversations yet</Text>
      <Text style={styles.emptySubtitle}>Start chatting with someone!</Text>
    </View>
  );

  return (
    <View style={styles.container}>
      <FlatList
        data={rooms}
        renderItem={renderRoom}
        keyExtractor={(item) => item.id}
        ListEmptyComponent={renderEmptyState}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={rooms.length === 0 ? styles.emptyListContainer : undefined}
        refreshControl={
          onRefresh ? (
            <RefreshControl
              refreshing={refreshing}
              onRefresh={onRefresh}
              colors={[colors.primary]}
              tintColor={colors.primary}
            />
          ) : undefined
        }
      />
    </View>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.cardBackground,
  },
  emptyListContainer: {
    flex: 1,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 15,
  },
  emptyIcon: {
    fontSize: 36,
    marginBottom: 10,
    color: colors.primary,
  },
  emptyTitle: {
    fontSize: 16,
    color: colors.toneDark1,
    marginBottom: 3,
    textAlign: 'center',
    fontFamily: 'Outfit-Bold',
  },
  emptySubtitle: {
    fontSize: 14,
    color: colors.toneDark1,
    opacity: 0.7,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 20,
    fontFamily: 'Outfit-Regular',
  },
  startChatButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 25,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 3,
  },
  startChatButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
});

export default RoomsList;
