import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { AuthModule } from './auth/auth.module';
import { ChatModule } from './chat/chat.module';
import { AdminModule } from './admin/admin.module';
import { NotificationsModule } from './notifications/notifications.module';
import { MediaModule } from './media/media.module';
import { User } from './auth/user.entity';
import { RefreshToken } from './auth/refresh-token.entity';
import { Message } from './chat/message.entity';
import { FcmToken } from './notifications/entities/fcm-token.entity';
import { Media } from './media/media.entity';
import { AppController } from './app.controller';
import { AppService } from './app.service';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => {
        // Check if DATABASE_URL is provided (for Render deployment)
        const databaseUrl = configService.get('DATABASE_URL');

        if (databaseUrl) {
          console.log('Using PostgreSQL database with connection URL');
          return {
            type: 'postgres',
            url: databaseUrl,
            entities: [User, RefreshToken, Message, FcmToken, Media],
            synchronize: configService.get('NODE_ENV') !== 'production',
            ssl: { rejectUnauthorized: false },
          };
        }

        // Use individual connection parameters if DATABASE_URL is not provided
        console.log('Using PostgreSQL database with individual parameters');
        return {
          type: 'postgres',
          host: configService.get('DB_HOST', 'localhost'),
          port: configService.get('DB_PORT', 5432),
          username: configService.get('DB_USERNAME', 'chatuser'),
          password: configService.get('DB_PASSWORD', 'chatpassword'),
          database: configService.get('DB_DATABASE', 'chatdb'),
          entities: [User, RefreshToken, Message, FcmToken, Media],
          synchronize: configService.get('NODE_ENV') !== 'production',
        };
      },
    }),
    AuthModule,
    ChatModule,
    AdminModule,
    NotificationsModule,
    MediaModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
