/**
 * FCM Service Tests
 * 
 * Basic tests to verify FCM service functionality
 */

import { fcmService } from '../fcmService';

// Mock Firebase messaging
jest.mock('@react-native-firebase/messaging', () => {
  return () => ({
    requestPermission: jest.fn(() => Promise.resolve(1)), // AUTHORIZED
    getToken: jest.fn(() => Promise.resolve('mock-fcm-token')),
    onMessage: jest.fn(),
    onNotificationOpenedApp: jest.fn(),
    getInitialNotification: jest.fn(() => Promise.resolve(null)),
    onTokenRefresh: jest.fn(),
    AuthorizationStatus: {
      AUTHORIZED: 1,
      DENIED: 0,
      NOT_DETERMINED: -1,
      PROVISIONAL: 2,
    },
  });
});

// Mock React Native modules
jest.mock('react-native', () => ({
  Platform: {
    OS: 'android',
    Version: 30,
  },
  Alert: {
    alert: jest.fn(),
  },
  PermissionsAndroid: {
    request: jest.fn(() => Promise.resolve('granted')),
    PERMISSIONS: {
      POST_NOTIFICATIONS: 'android.permission.POST_NOTIFICATIONS',
    },
    RESULTS: {
      GRANTED: 'granted',
      DENIED: 'denied',
    },
  },
}));

// Mock API service
jest.mock('../api', () => ({
  post: jest.fn(() => Promise.resolve({ data: { success: true } })),
}));

describe('FCM Service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('should be defined', () => {
    expect(fcmService).toBeDefined();
  });

  test('should initialize successfully', async () => {
    const result = await fcmService.initialize();
    expect(result).toBe(true);
  });

  test('should get FCM token', async () => {
    const token = await fcmService.getFCMToken();
    expect(token).toBe('mock-fcm-token');
  });

  test('should register token with backend', async () => {
    const result = await fcmService.registerTokenWithBackend('testuser');
    expect(result).toBe(true);
  });

  test('should check if service is initialized', () => {
    const isInitialized = fcmService.isServiceInitialized();
    expect(typeof isInitialized).toBe('boolean');
  });

  test('should get current token', () => {
    const token = fcmService.getCurrentToken();
    expect(typeof token === 'string' || token === null).toBe(true);
  });
});
