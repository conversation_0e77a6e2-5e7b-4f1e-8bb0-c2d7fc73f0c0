// auth/auth.service.ts
import { Injectable, UnauthorizedException, ConflictException, BadRequestException, Inject, forwardRef } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from './user.entity';
import * as bcrypt from 'bcrypt';
import { JwtService } from '@nestjs/jwt';
import { RefreshTokenService } from './refresh-token.service';

@Injectable()
export class AuthService {
  constructor(
    @InjectRepository(User)
    private userRepo: Repository<User>,
    private jwtService: JwtService,
    private refreshTokenService: RefreshTokenService,
    @Inject(forwardRef(() => require('../chat/chat.gateway').ChatGateway))
    private chatGateway: any, // Using any to avoid circular dependency issues
  ) {}

  async register(username: string, password: string) {
    if (!/^[a-zA-Z0-9_]{3,20}$/.test(username)) {
      throw new BadRequestException('Invalid username format');
    }

    if (!/^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/.test(password)) {
      throw new BadRequestException('Password too weak');
    }

    // Check if username already exists
    const existingUser = await this.userRepo.findOne({ where: { username } });
    if (existingUser) {
      throw new ConflictException('Username already exists');
    }

    const hashed = await bcrypt.hash(password, 10);
    const user = this.userRepo.create({ username, password: hashed });
    const savedUser = await this.userRepo.save(user);

    // Generate tokens for the new user
    const payload = {
      sub: savedUser.id,
      username: savedUser.username,
      isAdmin: savedUser.isAdmin
    };

    const accessToken = this.jwtService.sign(payload);
    const refreshToken = await this.refreshTokenService.generateRefreshToken(savedUser.id);

    return {
      user: savedUser,
      access_token: accessToken,
      refresh_token: refreshToken.token,
    };
  }

  async login(
    username: string,
    password: string,
    deviceInfo?: { deviceId?: string; userAgent?: string; ipAddress?: string }
  ) {
    const user = await this.userRepo.findOne({ where: { username } });
    if (!user) throw new UnauthorizedException('Invalid credentials');

    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) throw new UnauthorizedException('Invalid credentials');

    // Revoke all existing refresh tokens for this user (single-device authentication)
    await this.refreshTokenService.revokeAllUserTokens(user.id);

    // Immediately invalidate any active Socket.IO sessions for this user
    try {
      this.chatGateway?.invalidateUserSession(user.username, 'logged_in_elsewhere');
    } catch (error) {
      console.error('Failed to invalidate user session via Socket.IO:', error);
      // Don't fail the login if Socket.IO invalidation fails
    }

    const payload = {
      sub: user.id,
      username: user.username,
      isAdmin: user.isAdmin
    };

    // Generate both access and refresh tokens
    const accessToken = this.jwtService.sign(payload);
    const refreshToken = await this.refreshTokenService.generateRefreshToken(user.id, deviceInfo);

    return {
      access_token: accessToken,
      refresh_token: refreshToken.token,
    };
  }

  async refreshAccessToken(refreshTokenString: string) {
    const refreshToken = await this.refreshTokenService.validateRefreshToken(refreshTokenString);
    if (!refreshToken) {
      throw new UnauthorizedException('Invalid refresh token');
    }

    const payload = {
      sub: refreshToken.user.id,
      username: refreshToken.user.username,
      isAdmin: refreshToken.user.isAdmin
    };

    // Generate new access token
    const accessToken = this.jwtService.sign(payload);

    // Rotate refresh token for security
    const newRefreshToken = await this.refreshTokenService.rotateRefreshToken(
      refreshTokenString,
      refreshToken.user.id
    );

    return {
      access_token: accessToken,
      refresh_token: newRefreshToken.token,
    };
  }

  async logout(refreshTokenString: string) {
    await this.refreshTokenService.revokeRefreshToken(refreshTokenString);
  }

  async logoutAll(userId: string) {
    await this.refreshTokenService.revokeAllUserTokens(userId);
  }
}