import { appSchema, tableSchema } from '@nozbe/watermelondb';

// Define the database schema
export const schema = appSchema({
  version: 8, // Updated version to add media_id field
  tables: [
    // Chats table - stores individual messages
    tableSchema({
      name: 'chats',
      columns: [
        { name: 'room_id', type: 'string', isIndexed: true },
        { name: 'sender_username', type: 'string', isIndexed: true },
        { name: 'receiver_username', type: 'string', isIndexed: true },
        { name: 'message', type: 'string' },
        { name: 'type', type: 'string', isIndexed: true },
        { name: 'timestamp', type: 'number', isIndexed: true },
        { name: 'status', type: 'string' },
        { name: 'is_mine', type: 'boolean' },
        { name: 'media_id', type: 'string' },
        { name: 'media_uri', type: 'string' },
        { name: 'media_name', type: 'string' },
        { name: 'media_type', type: 'string' },
        { name: 'media_size', type: 'number' }
      ]
    }),

    // Rooms table - represents active conversations
    tableSchema({
      name: 'rooms',
      columns: [
        { name: 'room_id', type: 'string', isIndexed: true },
        { name: 'username', type: 'string', isIndexed: true },
        { name: 'last_msg', type: 'string' },
        { name: 'updated', type: 'number', isIndexed: true },
        { name: 'unread_count', type: 'number' }
      ]
    })
  ]
});
