.message-input-wrapper {
  position: relative;
  width: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

/* Ensure emoji bar is properly positioned on mobile */
@media (max-width: 767px) {
  .message-input-wrapper {
    padding-bottom: 4px;
  }
}

.message-input-form {
  display: flex;
  width: 100%;
  position: relative;
}

.message-input-container {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 10px 16px;
  border-radius: 0; /* No border radius on mobile */
  flex-wrap: nowrap;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 10;
  background-color: var(--card-background);
  border-top: 1px solid var(--border-color);
  box-sizing: border-box;
}

@media (min-width: 768px) {
  .message-input-container {
    padding: 12px 16px;
    position: relative;
    border-top: none;
    left: auto;
    right: auto;
    width: auto;
  }
}

.message-input {
  border: 1px solid #ddd;
  border-radius: 45px;
  padding: 12px 16px;
  font-size: 17px;
  line-height: 20px;
  min-height: 44px;
  max-height: 120px;
  resize: none;
  outline: none;
  font-family: inherit;
  min-width: 0; /* Allows flex item to shrink below content size */
  color: var(--text-color);
  transition: border-color 0.2s, box-shadow 0.2s;
  width: calc(100% - 120px); /* Account for media button + send button width + margins */
  display: block;
  box-sizing: border-box;
  overflow: hidden;
}

@media (min-width: 768px) {
  .message-input {
    border-radius: 22px;
    padding: 12px 16px;
    font-size: 15px;
    min-height: 46px;
    width: calc(100% - 120px);
  }
}

.message-input:focus {
  border-color: var(--primary-color);
  border-width: 2px;
  box-shadow: 0 0 0 2px var(--primary-color-light);
}

.message-input:disabled {
  color: #999;
}

.media-button {
  background-color: #f8f9fa;
  color: #6c757d;
  border: 1px solid #e9ecef;
  min-width: 44px;
  min-height: 44px;
  width: 44px;
  height: 44px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  flex-shrink: 0;
  transition: all 0.2s;
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  margin-right: 8px;
}

.media-button:hover {
  background-color: #e9ecef;
  color: #495057;
}

.media-button:disabled {
  background-color: #f8f9fa;
  color: #adb5bd;
  cursor: not-allowed;
  border-color: #e9ecef;
}

.upload-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid #e9ecef;
  border-top: 2px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.send-button {
  background-color: var(--primary-color);
  color: white;
  border: none;
  min-width: 44px;
  min-height: 44px;
  width: 44px;
  height: 44px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  flex-shrink: 0;
  transition: background-color 0.2s;
  box-shadow: 0 2px 4px rgba(240, 79, 61, 0.3);
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  margin-left: 8px;
}

.send-button svg {
  width: 22px;
  height: 22px;
}

@media (min-width: 768px) {
  .media-button {
    min-width: 46px;
    min-height: 46px;
    width: 46px;
    height: 46px;
    left: 0px;
  }

  .media-button svg {
    width: 22px;
    height: 22px;
  }

  .send-button {
    min-width: 46px;
    min-height: 46px;
    width: 46px;
    height: 46px;
    right: 0px;
  }

  .send-button svg {
    width: 24px;
    height: 24px;
  }
}

.send-button:hover {
  background-color: var(--primary-color-dark);
}

.send-button:disabled {
  background-color: #F8ADA5;
  cursor: not-allowed;
  box-shadow: none;
}

.send-status {
  position: absolute;
  top: -35px;
  left: 50%;
  transform: translateX(-50%);
  padding: 6px 12px;
  border-radius: var(--radius-sm);
  font-size: 12px;
  z-index: 10;
  box-shadow: var(--shadow-sm);
  animation: fadeIn 0.3s ease-in-out;
}

@media (min-width: 768px) {
  .send-status {
    top: -40px;
    padding: 8px 16px;
    font-size: 14px;
  }
}

.send-status.success {
  background-color: #FEE8E6;
  color: var(--primary-color);
}

.send-status.error {
  background-color: #ffebee;
  color: #c62828;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translate(-50%, 10px);
  }
  to {
    opacity: 1;
    transform: translate(-50%, 0);
  }
}

/* Mobile-first styles are now the default */
