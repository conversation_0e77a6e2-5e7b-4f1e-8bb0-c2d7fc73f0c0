import { useState } from 'react';
import './Settings.css';
import SettingModal from './SettingModal'; // Reusable modal
import ClearDataModal from './ClearDataModal'; // Modal for clearing data
import { useSelector } from 'react-redux';
import { selectUser } from '../redux/slices/authSlice';
import { chatDBService } from '../database/service';


type SettingType = 'password' | 'recall' | 'notifications' | null;

interface SettingsProps {
  onClose?: () => void;
  onLogout: () => void;
}

const Settings: React.FC<SettingsProps> = ({ onClose, onLogout }) => {
  const [modalType, setModalType] = useState<SettingType>(null);
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [showClearDataModal, setShowClearDataModal] = useState(false);
  const [clearingData, setClearingData] = useState(false);
  const currentUser = useSelector(selectUser);

  const handleToggleNotifications = () => {
    const newValue = !notificationsEnabled;
    setNotificationsEnabled(newValue);
    console.log('Saved notifications:', newValue);
  };

  const openModal = (type: SettingType) => setModalType(type);
  const closeModal = () => setModalType(null);

  const handleOpenClearDataModal = () => {
    setShowClearDataModal(true);
  };

  const handleCloseClearDataModal = () => {
    setShowClearDataModal(false);
  };

  const handleClearAllData = async () => {
    try {
      setClearingData(true);

      // Call the database service to clear all data
      const success = await chatDBService.clearAllData();

      if (success) {
        console.log('All data cleared successfully');
      } else {
        console.error('Failed to clear data');
      }

      setShowClearDataModal(false);
    } catch (error) {
      console.error('Error clearing data:', error);
    } finally {
      setClearingData(false);
    }
  };

  return (
    <div className="settings-window">
      <div className="settings-header">
        {/*<div>PROFILE SETTINGS</div>*/}
        {/*<button className="close-button" onClick={onClose}>×</button>*/}
      </div>

      <div className="settings-user-info">
        <div className="user-avatar-large">{currentUser?.charAt(0).toUpperCase()}</div>
        <div className="username-display">{currentUser}</div>
      </div>

 <div className="setting-group">
      <p>Profile Settings</p>

      <div className="setting-item">
        <span>Password</span>
        <div className="material-btn" onClick={() => setModalType('password')}>
          Change
        </div>
      </div>

      <div className="setting-item">
        <span>Recall Limit</span>
        <div className="material-btn" onClick={() => setModalType('recall')}>
          Set Limit
        </div>
      </div>

      <div className="setting-item">
        <span>Notifications</span>
          <div className="material-btn" >
        <label className="material-switch">
          <input
            type="checkbox"
            checked={notificationsEnabled}
            onChange={handleToggleNotifications}
          />
          <span className="slider"></span>
        </label>
        </div>
      </div>

      <div className="setting-item">
        <span>Clear Messages & Rooms</span>
        <div className="material-btn danger-btn" onClick={handleOpenClearDataModal}>
          Clear Data
        </div>
      </div>

      </div>

      <div className="settings-actions">
        <button onClick={onLogout} className="logout-button">Logout</button>
      </div>

      {modalType && (
        <SettingModal type={modalType} onClose={closeModal} />
      )}

      {showClearDataModal && (
        <ClearDataModal
          onClose={handleCloseClearDataModal}
          onConfirm={handleClearAllData}
          loading={clearingData}
        />
      )}
    </div>
  );
};

export default Settings;
