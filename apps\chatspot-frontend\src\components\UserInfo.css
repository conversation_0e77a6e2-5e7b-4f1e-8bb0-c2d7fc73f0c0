.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

@media (min-width: 768px) {
  .user-info {
    gap: 10px;

  }

}

.user-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  color: var(--primary-color-tone);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 18px;
}
.user-avatar-public{
  background-color: var(--tint-color-light);
}
.user-avatar-self{
  background-color: var(--primary-color-tint);
}

@media (min-width: 768px) {
  .user-avatar {
    width: 50px;
    height: 50px;
    font-size: 18px;

  }
}

.user-details {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-size: 14px;
  color: var(--text-color);
}

@media (min-width: 768px) {
  .user-name {
    font-size: 16px;
  }
}

/* For use in chat window header */
.chat-window-header .user-info {
  flex: 1;
}

/* For use in rooms list */
.room-item .user-info {
  width: 100%;
}

/* Emoji reaction in room list */
.emoji-reaction-message {
  display: flex;
  align-items: center;
}

.emoji-reaction-message .emoji-reaction-emoji {
  font-size: 30px;
  margin-right: 6px;
}

.emoji-reaction-message .emoji-reaction-mood {
  font-size: 12px;
  color: var(--primary-color);
  font-weight: 500;
  text-transform: capitalize;
}

@keyframes pulseEmoji {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* Chat status styles */
.chat-contact-status {
  font-size: 12px;
  color: #666;
  display: flex;
  align-items: center;
}

@media (min-width: 768px) {
  .chat-contact-status {
    font-size: 13px;
  }
}

/* Typing indicator styles */
.typing-text {
  margin-right: 5px;
}

.header-typing-indicator {
  display: inline-flex;
  align-items: center;
}

.header-typing-indicator span {
  height: 4px;
  width: 4px;
  margin: 0 1px;
  background-color: #666;
  border-radius: 50%;
  display: inline-block;
  animation: typingBounce 1.4s infinite ease-in-out both;
}

.header-typing-indicator span:nth-child(1) {
  animation-delay: -0.32s;
}

.header-typing-indicator span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typingBounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

/* Emoji reaction styles */
.emoji-reaction-status {
  display: flex;
  align-items: center;
  position: relative;
}

.emoji-reaction {
  font-size: 20px;
  animation: pulseEmoji 1.5s infinite;
  margin-right: 5px;
}

.emoji-mood {
  font-size: 12px;
  color: #666;
  margin-left: 4px;
}

@keyframes pulseEmoji {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}
