import { takeEvery, call, put } from 'redux-saga/effects';
import { mediaDownloadRequested, mediaDownloadSuccess, mediaDownloadFailure } from '../slices/mediaSlice';
import { messageAPI } from '../../services/api';
import { mediaUploadService } from '../../services/mediaUploadService';
import { chatDBService } from '../../database/service';

function* handleMediaDownload(action: ReturnType<typeof mediaDownloadRequested>): Generator<any, void, any> {
  const { mediaId } = action.payload;
  try {
    // 1. Fetch signed URL and media metadata
    const signedUrlResponse = yield call(messageAPI.getSignedUrl, mediaId);
    const signedUrl = signedUrlResponse.signedUrl;
    const mediaMetadata = signedUrlResponse.media;
    // 2. Download media to local file path using actual filename
    const localFileUri = yield call(
      mediaUploadService.downloadMediaToLocal,
      signedUrl,
      mediaMetadata.original_filename
    );
    // 3. Find messageId by mediaId
    const messageId = yield call(chatDBService.findMessageIdByMediaId, mediaId);
    if (messageId) {
      // 4. Update WatermelonDB message record
      yield call(
        chatDBService.updateMediaMessageMetadata,
        messageId,
        localFileUri,
        mediaMetadata.original_filename,
        mediaMetadata.mime_type,
        mediaMetadata.file_size
      );
    }
    // 5. Dispatch success
    yield put(
      mediaDownloadSuccess({
        mediaId,
        localUri: localFileUri,
        name: mediaMetadata.original_filename,
        type: mediaMetadata.mime_type,
        size: mediaMetadata.file_size,
      })
    );
  } catch (error: any) {
    yield put(mediaDownloadFailure({ mediaId, error: error.message || 'Failed to download media' }));
  }
}

export function* mediaSaga() {
  yield takeEvery(mediaDownloadRequested.type, handleMediaDownload);
} 