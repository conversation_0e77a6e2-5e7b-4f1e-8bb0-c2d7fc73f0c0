/**
 * Notification utilities for Firebase Cloud Messaging (FCM)
 */
import firebaseMessagingService from '../firebase/firebaseService';

// Check if Firebase Cloud Messaging is supported
export const isFCMSupported = async (): Promise<boolean> => {
  try {
    // This will check if the browser supports FCM
    const token = await firebaseMessagingService.getToken();
    return !!token;
  } catch (error) {
    console.warn('Firebase Cloud Messaging is not supported:', error);
    return false;
  }
};

// Request notification permission using Firebase
export const requestNotificationPermission = async (): Promise<boolean> => {
  try {
    // Use Firebase's permission request which handles all edge cases
    return await firebaseMessagingService.requestPermission();
  } catch (error) {
    console.error('Error requesting notification permission:', error);
    return false;
  }
};

// Get FCM token
export const getFCMToken = async (): Promise<string | null> => {
  try {
    return await firebaseMessagingService.getToken();
  } catch (error) {
    console.error('Error getting FCM token:', error);
    return null;
  }
};

export default {
  isFCMSupported,
  requestNotificationPermission,
  getFCMToken,
};
