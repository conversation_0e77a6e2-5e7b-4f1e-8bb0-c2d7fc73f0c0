import React, { useEffect } from 'react';
import {
  View,
  StyleSheet,
  SafeAreaView,
} from 'react-native';
import { useAppDispatch, useAppSelector } from '../hooks/redux';
import { selectAuthUser } from '../redux/slices/authSlice';
import { setCurrentReceiver, selectCurrentReceiverUsername } from '../redux/slices/chatDBSlice';
import { chatDBService } from '../database/service';
import { useWatermelonObservable } from '../hooks/useWatermelonObservable';
import { ChatRoomNavigationProp, ChatRoomRouteProp } from '../navigation/types';
import ChatWindow from './ChatWindow';

interface ChatRoomProps {
  route: ChatRoomRouteProp;
  navigation: ChatRoomNavigationProp;
}

const ChatRoom: React.FC<ChatRoomProps> = ({ route, navigation }) => {
  const dispatch = useAppDispatch();
  const { username } = route.params;
  const currentUser = useAppSelector(selectAuthUser);
  const selectedReceiverUsername = useAppSelector(selectCurrentReceiverUsername);

  // Use WatermelonDB observables for messages
  const messages = useWatermelonObservable(
    (currentUser && username) ?
      chatDBService.observeMessages(currentUser, username) :
      null,
    []
  );

  // Set the current receiver when the component mounts
  useEffect(() => {
    if (username) {
      dispatch(setCurrentReceiver(username));

      // Mark messages as read when the chat room is opened
      if (currentUser) {
        chatDBService.markMessagesAsRead(currentUser, username)
          .catch(error => console.error('Error marking messages as read:', error));
      }
    }
  }, [username, currentUser, dispatch]);

  // Handle back button click
  const handleBackToRooms = () => {
    dispatch(setCurrentReceiver(null));
    navigation.goBack();
  };

  // Handle showing profile
  const handleShowProfile = () => {
    if (username) {
      navigation.navigate('Profile', { username });
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <ChatWindow
          messages={messages}
          receiverUsername={username || ''}
          onClearChat={() => console.log('Chat cleared')}
          onBackToRooms={handleBackToRooms}
          onShowProfile={handleShowProfile}
          navigation={navigation}
        />
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
  },
});

export default ChatRoom;
