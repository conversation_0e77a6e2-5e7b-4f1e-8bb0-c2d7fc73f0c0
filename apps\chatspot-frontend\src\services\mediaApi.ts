import api from './api';

export interface MediaUploadResponse {
  id: string;
  original_filename: string;
  filename: string;
  mime_type: string;
  file_size: number;
  media_type: string;
  file_path: string;
  uploaded_by: string;
  width: number | null;
  height: number | null;
  s3_key: string | null;
  uploaded_at: string;
}

export interface SignedUrlResponse {
  media: MediaUploadResponse;
  signedUrl: string;
  expiresAt: string;
}

class MediaApiService {
  /**
   * Upload media file to backend
   */
  async uploadMedia(file: File): Promise<MediaUploadResponse> {
    const formData = new FormData();
    formData.append('file', file);

    const response = await api.post('/api/media/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return response.data;
  }

  /**
   * Get signed URL for media access
   */
  async getMediaSignedUrl(mediaId: string, expiresIn: number = 3600): Promise<SignedUrlResponse> {
    const response = await api.get(`/api/media/${mediaId}/signed-url?expiresIn=${expiresIn}`);
    return response.data;
  }

  /**
   * Delete media file
   */
  async deleteMedia(mediaId: string): Promise<void> {
    await api.delete(`/api/media/${mediaId}`);
  }

  /**
   * Get media info by ID
   */
  async getMediaInfo(mediaId: string): Promise<MediaUploadResponse> {
    const response = await api.get(`/api/media/${mediaId}`);
    return response.data;
  }
}

export const mediaApiService = new MediaApiService(); 