import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface MediaDownloadState {
  byId: {
    [mediaId: string]: {
      status: 'idle' | 'downloading' | 'success' | 'failure';
      localUri?: string;
      name?: string;
      type?: string;
      size?: number;
      error?: string;
    };
  };
}

const initialState: MediaDownloadState = {
  byId: {},
};

const mediaSlice = createSlice({
  name: 'media',
  initialState,
  reducers: {
    mediaDownloadRequested: (state, action: PayloadAction<{ mediaId: string }>) => {
      state.byId[action.payload.mediaId] = {
        status: 'downloading',
      };
    },
    mediaDownloadSuccess: (state, action: PayloadAction<{ mediaId: string; localUri: string; name: string; type: string; size: number }>) => {
      state.byId[action.payload.mediaId] = {
        status: 'success',
        localUri: action.payload.localUri,
        name: action.payload.name,
        type: action.payload.type,
        size: action.payload.size,
      };
    },
    mediaDownloadFailure: (state, action: PayloadAction<{ mediaId: string; error: string }>) => {
      state.byId[action.payload.mediaId] = {
        status: 'failure',
        error: action.payload.error,
      };
    },
  },
});

export const { mediaDownloadRequested, mediaDownloadSuccess, mediaDownloadFailure } = mediaSlice.actions;
export default mediaSlice.reducer; 