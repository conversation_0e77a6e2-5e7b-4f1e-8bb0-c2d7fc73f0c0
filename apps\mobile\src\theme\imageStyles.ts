/**
 * Image styling utilities matching chatspot-frontend
 * These styles match the image styling from chatspot-frontend/src/index.css
 */

import { StyleSheet } from 'react-native';
import { colors, radius, spacing } from './colors';

// Image container styles matching frontend .image-div
export const imageStyles = StyleSheet.create({
  // Main image container matching .image-div
  imageContainer: {
    borderRadius: radius.md, // 12px equivalent
    backgroundColor: colors.imageBg, // rgba(255, 255, 255, 0.8)
    overflow: 'hidden',
    margin: 0,
    padding: 0,
  },
  
  // Image styles matching .image-div img
  image: {
    maxWidth: 200,
    maxHeight: 200,
    borderRadius: radius.md, // 12px equivalent
    resizeMode: 'cover',
  },
  
  // Message image container for sent messages
  sentImageContainer: {
    borderRadius: radius.md,
    backgroundColor: colors.imageBg,
    overflow: 'hidden',
    alignSelf: 'flex-end',
    marginVertical: spacing.xs,
  },
  
  // Message image container for received messages
  receivedImageContainer: {
    borderRadius: radius.md,
    backgroundColor: colors.imageBg,
    overflow: 'hidden',
    alignSelf: 'flex-start',
    marginVertical: spacing.xs,
  },
  
  // Avatar image styles
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25, // circular
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  
  avatarLarge: {
    width: 80,
    height: 80,
    borderRadius: 40, // circular
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  
  avatarSmall: {
    width: 32,
    height: 32,
    borderRadius: 16, // circular
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  
  // Avatar text styles
  avatarText: {
    color: colors.white,
    fontSize: 18,
    fontWeight: 'bold',
  },
  
  avatarTextLarge: {
    color: colors.white,
    fontSize: 28,
    fontWeight: 'bold',
  },
  
  avatarTextSmall: {
    color: colors.white,
    fontSize: 12,
    fontWeight: 'bold',
  },
  
  // Profile image styles
  profileImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: colors.gray200,
    borderWidth: 2,
    borderColor: colors.primary,
  },
  
  // Thumbnail styles for image previews
  thumbnail: {
    width: 60,
    height: 60,
    borderRadius: radius.sm,
    backgroundColor: colors.gray200,
    resizeMode: 'cover',
  },
  
  // Full screen image styles
  fullScreenImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'contain',
    backgroundColor: colors.black,
  },
  
  // Image placeholder styles
  imagePlaceholder: {
    width: 200,
    height: 200,
    borderRadius: radius.md,
    backgroundColor: colors.gray200,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.border,
    borderStyle: 'dashed',
  },
  
  imagePlaceholderText: {
    color: colors.textSecondary,
    fontSize: 14,
    textAlign: 'center',
  },
  
  // Loading overlay for images
  imageLoadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: colors.overlay,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: radius.md,
  },
  
  // Error state for images
  imageError: {
    width: 200,
    height: 200,
    borderRadius: radius.md,
    backgroundColor: colors.gray100,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.danger,
  },
  
  imageErrorText: {
    color: colors.danger,
    fontSize: 14,
    textAlign: 'center',
    marginTop: spacing.sm,
  },
});

// Image utility functions
export const imageUtils = {
  // Get avatar background color based on username
  getAvatarColor: (username: string): string => {
    const colors = [
      '#F0503E', // primary
      '#238b97', // secondary
      '#4CAF50', // success
      '#FF9800', // orange
      '#9C27B0', // purple
      '#2196F3', // blue
      '#795548', // brown
      '#607D8B', // blue grey
    ];
    
    const index = username.length % colors.length;
    return colors[index];
  },
  
  // Get initials from username
  getInitials: (username: string): string => {
    if (!username) return '?';
    
    const words = username.trim().split(' ');
    if (words.length === 1) {
      return words[0].charAt(0).toUpperCase();
    }
    
    return (words[0].charAt(0) + words[words.length - 1].charAt(0)).toUpperCase();
  },
  
  // Calculate image dimensions while maintaining aspect ratio
  calculateImageDimensions: (
    originalWidth: number,
    originalHeight: number,
    maxWidth: number = 200,
    maxHeight: number = 200
  ): { width: number; height: number } => {
    const aspectRatio = originalWidth / originalHeight;
    
    let width = originalWidth;
    let height = originalHeight;
    
    if (width > maxWidth) {
      width = maxWidth;
      height = width / aspectRatio;
    }
    
    if (height > maxHeight) {
      height = maxHeight;
      width = height * aspectRatio;
    }
    
    return { width: Math.round(width), height: Math.round(height) };
  },
};

export default imageStyles;
