if(NOT TARGET react-native-nitro-modules::NitroModules)
add_library(react-native-nitro-modules::NitroModules SHARED IMPORTED)
set_target_properties(react-native-nitro-modules::NitroModules PROPERTIES
    IMPORTED_LOCATION "C:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-nitro-modules/android/build/intermediates/cxx/Debug/69553y5a/obj/x86_64/libNitroModules.so"
    INTERFACE_INCLUDE_DIRECTORIES "C:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-nitro-modules/android/build/headers/nitromodules"
    INTERFACE_LINK_LIBRARIES ""
)
endif()

