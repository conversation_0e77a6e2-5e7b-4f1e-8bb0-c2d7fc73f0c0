/**
 * Utility functions for handling user data
 */

// Key for storing the previous username in localStorage
const PREVIOUS_USERNAME_KEY = 'previous_username';

/**
 * Check if there's existing data for a different user
 * @param newUsername The username of the user trying to log in
 * @returns Object with information about previous user data
 */
export const checkPreviousUserData = (newUsername: string): { 
  hasPreviousData: boolean; 
  previousUsername: string | null;
} => {
  const previousUsername = localStorage.getItem(PREVIOUS_USERNAME_KEY);
  
  // If there's no previous username stored, or it's the same as the new one,
  // then there's no conflict
  if (!previousUsername || previousUsername === newUsername) {
    return { 
      hasPreviousData: false, 
      previousUsername: null 
    };
  }
  
  // There's a different username stored, which means we have data for a different user
  return { 
    hasPreviousData: true, 
    previousUsername 
  };
};

/**
 * Store the current username as the previous username
 * @param username The username to store
 */
export const storePreviousUsername = (username: string): void => {
  localStorage.setItem(PREVIOUS_USERNAME_KEY, username);
};

/**
 * Clear the stored previous username
 */
export const clearPreviousUsername = (): void => {
  localStorage.removeItem(PREVIOUS_USERNAME_KEY);
};
