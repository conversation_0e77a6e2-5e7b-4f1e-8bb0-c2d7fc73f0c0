import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faImage, 
  faVideo, 
  faMusic, 
  faFileAlt,
  faTimes 
} from '@fortawesome/free-solid-svg-icons';
import './MediaPickerModal.css';

interface MediaPickerModalProps {
  isVisible: boolean;
  onClose: () => void;
  onSelectImage: () => void;
  onSelectVideo: () => void;
  onSelectAudio: () => void;
  onSelectDocument: () => void;
}

const MediaPickerModal: React.FC<MediaPickerModalProps> = ({
  isVisible,
  onClose,
  onSelectImage,
  onSelectVideo,
  onSelectAudio,
  onSelectDocument
}) => {
  if (!isVisible) return null;

  const mediaOptions = [
    {
      id: 'image',
      label: 'Photo',
      description: 'Select images from your device',
      icon: faImage,
      color: '#4CAF50',
      onClick: onSelectImage
    },
    {
      id: 'video',
      label: 'Video',
      description: 'Select video files',
      icon: faVideo,
      color: '#2196F3',
      onClick: onSelectVideo
    },
    {
      id: 'audio',
      label: 'Audio',
      description: 'Select audio files',
      icon: faMusic,
      color: '#FF9800',
      onClick: onSelectAudio
    },
    {
      id: 'document',
      label: 'Document',
      description: 'Select documents and files',
      icon: faFileAlt,
      color: '#9C27B0',
      onClick: onSelectDocument
    }
  ];

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal-content" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h3>Select Media Type</h3>
          <button className="close-button" onClick={onClose}>
            <FontAwesomeIcon icon={faTimes} />
          </button>
        </div>
        
        <div className="modal-body">
          <div className="media-options-grid">
            {mediaOptions.map((option) => (
              <button
                key={option.id}
                className="media-option-button"
                onClick={option.onClick}
              >
                <div 
                  className="media-option-icon"
                  style={{ backgroundColor: option.color }}
                >
                  <FontAwesomeIcon icon={option.icon} />
                </div>
                <div className="media-option-content">
                  <span className="media-option-label">{option.label}</span>
                  <span className="media-option-description">{option.description}</span>
                </div>
              </button>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default MediaPickerModal;
