import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ActivityIndicator,
} from 'react-native';
import { radius, spacing, typography, useTheme, shadows } from '../theme';
import MediaMessageItem from './MediaMessageItem';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons'; // Note the updated icon set

interface MessageItemProps {
  message?: any;
  formatTime: (timestamp: number) => string;
  isLastInGroup: boolean;
}

const MessageItem: React.FC<MessageItemProps> = ({
  message,
  formatTime,
  isLastInGroup
}) => {
  const { colors } = useTheme();
  const styles = createStyles(colors);



  if (!message) return null;

  if (message.type === 'clear_chat') {
    return (
      <View style={styles.systemMessageContainer}>
        <View style={[
          styles.systemMessageContent,
          message.status === 'sending' && styles.sendingGradient
        ]}>
          <Text style={styles.systemMessageText}>
            Chat cleared by {message.is_mine ? 'you' : (message.sender_username || 'Unknown User')}
          </Text>
          <Text style={styles.messageTimeForClear}>{formatTime(message.timestamp)}</Text>
        </View>
      </View>
    );
  }

  if (message.type === 'typing') return null;

  // Handle media messages
  if (message.type === 'media') {
    return (
      <MediaMessageItem
        message={message}
        formatTime={formatTime}
        isLastInGroup={isLastInGroup}
      />
    );
  }

  const renderStatusIcon = () => {
  if (!message.isMine && !message.is_mine) return null;

  switch (message.status) {
    case 'sending':
      return (
        <View style={styles.statusContainer}>
          <Icon name="checkbox-blank-circle-outline" size={16} color={colors.primary} />
        </View>
      );
    case 'sent':
      return (
        <View style={styles.statusContainer}>
          <Icon name="check-circle-outline" size={16} color={colors.primary} />
        </View>
      );
    case 'delivered':
      return (
        <View style={styles.statusContainer}>
          <Icon name="check-circle" size={16} color={colors.primary} />
        </View>
      );
    case 'read':
      return (
        <View style={styles.statusContainer}>
          <Icon name="check-decagram" size={16} color={colors.success} />
        </View>
      );
    default:
      return null;
  }
};

  const isMine = message.isMine || message.is_mine;

  return (
    <View style={[
      styles.messageContainer,
      isMine ? styles.sentMessage : styles.receivedMessage,
      isLastInGroup && styles.lastInGroup
    ]}>
      <View style={[
        styles.messageContent,
        isMine ? styles.sentContent : styles.receivedContent,
        message.status === 'sending' && styles.sendingGradient,
        isLastInGroup && (isMine ? styles.lastMine : styles.lastPeer)
      ]}>

        <Text style={[
          styles.messageText,
          isMine ? styles.sentText : styles.receivedText
        ]}>
          {message.message || ''}
        </Text>
        <View style={styles.messageInfo}>
          <Text style={[
            styles.messageTime,
            isMine ? styles.sentTime : styles.receivedTime
          ]}>
            {formatTime(message.timestamp)}
          </Text>

          {renderStatusIcon()}
        </View>
      </View>
    </View>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  messageContainer: {
    marginVertical: 1,
    marginHorizontal: spacing.md,
  },
  sentMessage: {
    alignItems: 'flex-end',
  },
  receivedMessage: {
    alignItems: 'flex-start',
  },
  lastInGroup: {
    marginBottom: spacing.sm,
  },
  messageContent: {
    maxWidth: '80%',
    borderRadius: radius.lg,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
  },
  sentContent: {
    backgroundColor: colors.primaryLight3, // Use new message-specific colors
  },
  receivedContent: {
    backgroundColor: colors.toneLight3, // Use new message-specific colors
  },
  sendingGradient: {
    opacity: 0.7,
  },
  messageText: {
    fontSize: 14,
    fontFamily: 'Outfit-Regular',
    lineHeight: 16,
  },
  sentText: {
    color: colors.myMessageText, // Use new message-specific colors
  },
  receivedText: {
    color: colors.toneDark2, // Use new message-specific colors
  },
  sentTime: {
    color: colors.primary,
    fontFamily: 'Outfit-Bold',
  },
  receivedTime: {
    color: colors.toneDark2,
    fontFamily: 'Outfit-Bold',
  },
  messageInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: spacing.xs,
  },
  messageTime: {
    marginRight: spacing.xs,
    fontSize: 12,
    fontFamily: 'Outfit-Regular',
    lineHeight: 16,
  },
  messageTimeForClear:{
    color:colors.toneDark1,
    fontSize: 12,
    fontFamily: 'Outfit-Regular',
  },
  statusContainer: {
    marginLeft: spacing.xs,
  },
  singleTick: {
    fontSize: 12,
    fontFamily: 'Outfit-Regular',
  },
  doubleTick: {
    fontSize: 12,
    fontFamily: 'Outfit-Regular',
  },
  systemMessageContainer: {
    alignItems: 'center',
    marginVertical: spacing.sm,
  },
  systemMessageContent: {
    backgroundColor: colors.gray100,
    borderRadius: radius.md,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    alignItems: 'center',
  },
  systemMessageText: {
    fontSize: 14,
    fontFamily: 'Outfit-Regular',
    color: colors.textSecondary,
    textAlign: 'center',
  },
  lastMine: {
    borderBottomRightRadius: 0,
  },
  lastPeer: {
    borderBottomLeftRadius: 0,
  },

});

export default MessageItem;
