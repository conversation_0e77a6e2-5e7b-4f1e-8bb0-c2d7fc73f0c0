{"name": "chatspot", "version": "1.0.0", "description": "ChatSpot Messenger - A full-stack chat application with web and mobile clients", "private": true, "workspaces": ["apps/auth-service", "apps/chatspot-admin", "apps/chatspot-backend", "apps/chatspot-frontend", "packages/*"], "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "install-all": "yarn install", "backend": "yarn workspace chatspot-backend start:dev", "backend:localhost": "yarn workspace chatspot-backend start:localhost", "frontend": "yarn workspace chatspot-frontend dev", "admin": "yarn workspace chatspot-admin dev", "dev:web": "concurrently \"yarn run backend\" \"yarn run frontend\"", "dev:admin": "concurrently \"yarn run backend\" \"yarn run admin\"", "test:backend": "yarn workspace chatspot-backend test", "test:frontend": "yarn workspace chatspot-frontend test", "test:all": "concurrently \"yarn run test:backend\" \"yarn run test:frontend\"", "build:backend": "yarn workspace chatspot-backend build", "build:frontend": "yarn workspace chatspot-frontend build", "build:admin": "yarn workspace chatspot-admin build", "build:all": "yarn run build:backend && yarn run build:frontend && yarn run build:admin", "clean": "rimraf node_modules && rimraf apps/*/node_modules && rimraf packages/*/node_modules", "create-admin": "yarn workspace chatspot-backend create-admin", "reset-db": "yarn workspace chatspot-backend reset-db"}, "keywords": ["chat", "messenger", "react", "<PERSON><PERSON><PERSON>", "monorepo"], "author": "", "license": "ISC", "devDependencies": {"concurrently": "^9.1.2", "rimraf": "^5.0.5"}, "engines": {"node": ">=14.0.0", "npm": ">=6.0.0"}, "dependencies": {}}