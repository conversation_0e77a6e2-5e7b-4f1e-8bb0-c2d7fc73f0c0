<p align="center">
  <a href="http://nestjs.com/" target="blank"><img src="https://nestjs.com/img/logo-small.svg" width="120" alt="Nest Logo" /></a>
</p>

[circleci-image]: https://img.shields.io/circleci/build/github/nestjs/nest/master?token=abc123def456
[circleci-url]: https://circleci.com/gh/nestjs/nest

  <p align="center">A progressive <a href="http://nodejs.org" target="_blank">Node.js</a> framework for building efficient and scalable server-side applications.</p>
    <p align="center">
<a href="https://www.npmjs.com/~nestjscore" target="_blank"><img src="https://img.shields.io/npm/v/@nestjs/core.svg" alt="NPM Version" /></a>
<a href="https://www.npmjs.com/~nestjscore" target="_blank"><img src="https://img.shields.io/npm/l/@nestjs/core.svg" alt="Package License" /></a>
<a href="https://www.npmjs.com/~nestjscore" target="_blank"><img src="https://img.shields.io/npm/dm/@nestjs/common.svg" alt="NPM Downloads" /></a>
<a href="https://circleci.com/gh/nestjs/nest" target="_blank"><img src="https://img.shields.io/circleci/build/github/nestjs/nest/master" alt="CircleCI" /></a>
<a href="https://discord.gg/G7Qnnhy" target="_blank"><img src="https://img.shields.io/badge/discord-online-brightgreen.svg" alt="Discord"/></a>
<a href="https://opencollective.com/nest#backer" target="_blank"><img src="https://opencollective.com/nest/backers/badge.svg" alt="Backers on Open Collective" /></a>
<a href="https://opencollective.com/nest#sponsor" target="_blank"><img src="https://opencollective.com/nest/sponsors/badge.svg" alt="Sponsors on Open Collective" /></a>
  <a href="https://paypal.me/kamilmysliwiec" target="_blank"><img src="https://img.shields.io/badge/Donate-PayPal-ff3f59.svg" alt="Donate us"/></a>
    <a href="https://opencollective.com/nest#sponsor"  target="_blank"><img src="https://img.shields.io/badge/Support%20us-Open%20Collective-41B883.svg" alt="Support us"></a>
  <a href="https://twitter.com/nestframework" target="_blank"><img src="https://img.shields.io/twitter/follow/nestframework.svg?style=social&label=Follow" alt="Follow us on Twitter"></a>
</p>
  <!--[![Backers on Open Collective](https://opencollective.com/nest/backers/badge.svg)](https://opencollective.com/nest#backer)
  [![Sponsors on Open Collective](https://opencollective.com/nest/sponsors/badge.svg)](https://opencollective.com/nest#sponsor)-->

## Description

[Nest](https://github.com/nestjs/nest) framework TypeScript starter repository.

## Project setup

```bash
$ npm install
```

## Compile and run the project

```bash
# development
$ npm run start

# watch mode
$ npm run start:dev

# production mode
$ npm run start:prod
```

## Run tests

```bash
# unit tests
$ npm run test

# e2e tests
$ npm run test:e2e

# test coverage
$ npm run test:cov
```

## Deployment

When you're ready to deploy your NestJS application to production, there are some key steps you can take to ensure it runs as efficiently as possible. Check out the [deployment documentation](https://docs.nestjs.com/deployment) for more information.

If you are looking for a cloud-based platform to deploy your NestJS application, check out [Mau](https://mau.nestjs.com), our official platform for deploying NestJS applications on AWS. Mau makes deployment straightforward and fast, requiring just a few simple steps:

```bash
$ npm install -g @nestjs/mau
$ mau deploy
```

With Mau, you can deploy your application in just a few clicks, allowing you to focus on building features rather than managing infrastructure.

## Resources

Check out a few resources that may come in handy when working with NestJS:

- Visit the [NestJS Documentation](https://docs.nestjs.com) to learn more about the framework.
- For questions and support, please visit our [Discord channel](https://discord.gg/G7Qnnhy).
- To dive deeper and get more hands-on experience, check out our official video [courses](https://courses.nestjs.com/).
- Deploy your application to AWS with the help of [NestJS Mau](https://mau.nestjs.com) in just a few clicks.
- Visualize your application graph and interact with the NestJS application in real-time using [NestJS Devtools](https://devtools.nestjs.com).
- Need help with your project (part-time to full-time)? Check out our official [enterprise support](https://enterprise.nestjs.com).
- To stay in the loop and get updates, follow us on [X](https://x.com/nestframework) and [LinkedIn](https://linkedin.com/company/nestjs).
- Looking for a job, or have a job to offer? Check out our official [Jobs board](https://jobs.nestjs.com).

## Support

Nest is an MIT-licensed open source project. It can grow thanks to the sponsors and support by the amazing backers. If you'd like to join them, please [read more here](https://docs.nestjs.com/support).

## Stay in touch

- Author - [Kamil Myśliwiec](https://twitter.com/kammysliwiec)
- Website - [https://nestjs.com](https://nestjs.com/)
- Twitter - [@nestframework](https://twitter.com/nestframework)

## License

Nest is [MIT licensed](https://github.com/nestjs/nest/blob/master/LICENSE).

# ChatSpot Backend

A NestJS backend for the ChatSpot messaging application.

## Features

- User authentication and authorization
- Real-time messaging via WebSockets
- Media file management with signed URLs
- Push notifications
- Admin panel support

## Media Management with Signed URLs

The backend now supports secure media access using signed URLs instead of direct file streaming. This approach is more scalable, secure, and efficient.

### New Media Endpoints

#### 1. Get Signed URL for Media Access
```http
GET /api/media/{id}/signed-url?expiresIn=3600
Authorization: Bearer <jwt_token>
```

**Response:**
```json
{
  "media": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "original_filename": "vacation_photo.jpg",
    "mime_type": "image/jpeg",
    "file_size": 2048576,
    "media_type": "image",
    "uploaded_by": "johndoe",
    "uploaded_at": "2023-10-15T14:30:00Z"
  },
  "signedUrl": "https://bucket.s3.amazonaws.com/media/file.jpg?X-Amz-Algorithm=...",
  "expiresAt": "2023-10-15T15:30:00Z"
}
```

#### 2. Generate Upload URL for Direct S3 Upload
```http
POST /api/media/upload-url
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "filename": "my_photo.jpg",
  "contentType": "image/jpeg",
  "expiresIn": 3600
}
```

**Response:**
```json
{
  "uploadUrl": "https://bucket.s3.amazonaws.com/media/temp-file.jpg?X-Amz-Algorithm=...",
  "mediaId": "123e4567-e89b-12d3-a456-426614174000",
  "s3Key": "media/temp-file.jpg",
  "expiresAt": "2023-10-15T15:30:00Z"
}
```

#### 3. Complete Upload and Update Metadata
```http
PUT /api/media/{id}/complete-upload
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "fileSize": 2048576,
  "width": 1920,
  "height": 1080
}
```

### Benefits of Signed URLs

1. **Security**: URLs expire automatically and are tied to specific users
2. **Scalability**: Reduces server load by offloading file delivery to S3
3. **Performance**: Faster file access through CDN
4. **Cost Efficiency**: Reduces bandwidth costs on your server
5. **Flexibility**: Supports both S3 and local storage

### Usage Flow

1. **Upload Process:**
   - Client requests upload URL
   - Client uploads directly to S3 using the signed URL
   - Client calls complete-upload to update metadata

2. **Download Process:**
   - Client requests signed URL for media access
   - Client uses signed URL to download file directly from S3

### Migration from Direct Streaming

The original `/api/media/{id}` endpoint is still available but marked as deprecated. New clients should use the signed URL approach for better performance and security.

## Environment Variables

```env
# Database
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_USERNAME=postgres
DATABASE_PASSWORD=password
DATABASE_NAME=chatspot

# JWT
JWT_SECRET=your-secret-key

# S3 (Optional - for signed URLs)
USE_S3_STORAGE=true
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=your-bucket-name

# API Base URL (for local signed URLs)
API_BASE_URL=http://localhost:3000
```

## Installation

```bash
npm install
```

## Running the app

```bash
# development
npm run start

# watch mode
npm run start:dev

# production mode
npm run start:prod
```

## Test

```bash
# unit tests
npm run test

# e2e tests
npm run test:e2e

# test coverage
npm run test:cov
```
