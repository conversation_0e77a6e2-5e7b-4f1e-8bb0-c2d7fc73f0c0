# Reactotron Setup Guide

Reactotron is now configured for debugging your React Native app with Redux and Redux Saga.

## Installation

The following packages have been installed:
- `reactotron-react-native` - Core Reactotron for React Native
- `reactotron-redux` - Redux integration
- `reactotron-redux-saga` - Redux Saga integration

## Desktop App

1. Download and install the Reactotron desktop app from:
   https://github.com/infinitered/reactotron/releases

2. Launch the Reactotron desktop app

3. Make sure it's listening on port 9090 (default)

## Configuration

The Reactotron configuration is located in:
`src/config/ReactotronConfig.ts`

Key features enabled:
- Redux state monitoring
- Redux Saga effect monitoring
- Network request monitoring
- React Native debugging features

## Usage

1. Start the Reactotron desktop app
2. Run your React Native app: `npm start` and `npx react-native run-android`
3. The app should automatically connect to Reactotron

## Features Available

### Redux State
- View current Redux state
- Monitor state changes in real-time
- Dispatch actions manually

### Redux Saga
- Monitor saga effects
- See saga flow and execution
- Debug async operations

### Network
- Monitor API calls
- View request/response data
- Debug network issues

### React Native
- View component hierarchy
- Monitor performance
- Debug navigation

## Troubleshooting

### Connection Issues
- Make sure Reactotron desktop app is running
- Check that port 9090 is available
- For physical devices, update the host in `ReactotronConfig.ts` to your computer's IP address

### Redux Not Showing
- Ensure the store is properly configured with Reactotron enhancer
- Check that actions are being dispatched

### Saga Not Showing
- Verify saga middleware is configured with Reactotron monitor
- Check that sagas are running

## Configuration for Physical Device

If using a physical device instead of emulator:

1. Find your computer's IP address
2. Update `ReactotronConfig.ts`:
   ```typescript
   .configure({
     name: 'ChatSpot Mobile',
     host: '*************', // Replace with your IP
     port: 9090,
   })
   ```

## Production

Reactotron is automatically disabled in production builds (`__DEV__` check).
