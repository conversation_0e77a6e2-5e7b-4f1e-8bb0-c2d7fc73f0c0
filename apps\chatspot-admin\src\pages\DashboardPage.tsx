import { useEffect } from 'react';
import { useSelector } from 'react-redux';
import {
  Box,
  Grid,
  Paper,
  Typography,
  Card,
  CardContent,
  CardHeader,
  CircularProgress
} from '@mui/material';
import PeopleIcon from '@mui/icons-material/People';
import MessageIcon from '@mui/icons-material/Message';
import AdminLayout from '../components/AdminLayout';
import { fetchAllUsers, selectUsers, selectUsersLoading } from '../redux/slices/usersSlice';
import { useAppDispatch } from '../redux/hooks';

const DashboardPage = () => {
  const dispatch = useAppDispatch();
  const users = useSelector(selectUsers);
  const isLoading = useSelector(selectUsersLoading);

  useEffect(() => {
    dispatch(fetchAllUsers());
  }, [dispatch]);

  const stats = [
    {
      title: 'Total Users',
      value: users.length,
      icon: <PeopleIcon sx={{ fontSize: 40 }} />,
      color: '#3f51b5',
    },
    {
      title: 'Admin Users',
      value: users.filter(user => user.isAdmin).length,
      icon: <PeopleIcon sx={{ fontSize: 40 }} />,
      color: '#f50057',
    },
    {
      title: 'Regular Users',
      value: users.filter(user => !user.isAdmin).length,
      icon: <PeopleIcon sx={{ fontSize: 40 }} />,
      color: '#4caf50',
    },
    {
      title: 'Messages',
      value: '—', // This would come from a messages API
      icon: <MessageIcon sx={{ fontSize: 40 }} />,
      color: '#ff9800',
    },
  ];

  return (
    <AdminLayout title="Dashboard">
      <Box sx={{ flexGrow: 1 }}>
        <Grid container spacing={3}>
          {isLoading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', width: '100%', mt: 4 }}>
              <CircularProgress />
            </Box>
          ) : (
            <>
              {stats.map((stat, index) => (
                <Grid item xs={12} sm={6} md={3} key={index}>
                  <Paper
                    sx={{
                      p: 2,
                      display: 'flex',
                      flexDirection: 'column',
                      height: 140,
                      borderTop: `4px solid ${stat.color}`,
                    }}
                  >
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Typography component="h2" variant="h6" color="primary" gutterBottom>
                        {stat.title}
                      </Typography>
                      <Box sx={{ color: stat.color }}>{stat.icon}</Box>
                    </Box>
                    <Typography component="p" variant="h4">
                      {stat.value}
                    </Typography>
                  </Paper>
                </Grid>
              ))}

              <Grid item xs={12}>
                <Paper sx={{ p: 2, display: 'flex', flexDirection: 'column' }}>
                  <Typography component="h2" variant="h6" color="primary" gutterBottom>
                    Recent Users
                  </Typography>
                  <Box sx={{ overflowX: 'auto' }}>
                    <table style={{ width: '100%', borderCollapse: 'collapse' }}>
                      <thead>
                        <tr>
                          <th style={{ textAlign: 'left', padding: '8px' }}>Username</th>
                          <th style={{ textAlign: 'left', padding: '8px' }}>ID</th>
                          <th style={{ textAlign: 'left', padding: '8px' }}>Admin</th>
                        </tr>
                      </thead>
                      <tbody>
                        {users.slice(0, 5).map((user) => (
                          <tr key={user.id}>
                            <td style={{ padding: '8px' }}>{user.username}</td>
                            <td style={{ padding: '8px' }}>{user.id}</td>
                            <td style={{ padding: '8px' }}>{user.isAdmin ? 'Yes' : 'No'}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </Box>
                </Paper>
              </Grid>
            </>
          )}
        </Grid>
      </Box>
    </AdminLayout>
  );
};

export default DashboardPage;
