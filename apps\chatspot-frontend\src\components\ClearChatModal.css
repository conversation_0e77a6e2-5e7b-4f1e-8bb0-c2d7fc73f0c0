.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: var(--card-background);
  border-radius: var(--radius-md);
  width: 95%;
  max-width: 350px;
  box-shadow: var(--shadow-lg);
  animation: modalFadeIn 0.3s ease-out;
}

@media (min-width: 768px) {
  .modal-content {
    width: 90%;
    max-width: 450px;
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
}

@media (min-width: 768px) {
  .modal-header {
    padding: 16px 20px;
  }
}

.modal-header h3 {
  margin: 0;
  color: var(--primary-color);
  font-size: 16px;
  font-weight: 600;
}

@media (min-width: 768px) {
  .modal-header h3 {
    font-size: 18px;
  }
}

.close-button {
  background: none;
  border: none;
  font-size: 22px;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0;
  line-height: 1;
  min-height: auto;
}

@media (min-width: 768px) {
  .close-button {
    font-size: 24px;
  }
}

.close-button:hover {
  color: var(--text-color);
}

.modal-body {
  padding: 20px;
}

.warning-text {
  font-weight: 500;
  font-size: 16px;
  margin-bottom: 12px;
  color: #333;
}

.info-text {
  font-size: 14px;
  color: #666;
  margin-bottom: 0;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 0;
}

.cancel-button {
  background-color: #f5f5f5;
  color: #333;
  border: none;
  padding: 10px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
}

.cancel-button:hover {
  background-color: #e0e0e0;
}

.cancel-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.clear-chat-button {
  background-color: orange;
  color: white;
  border: none;
  padding: 10px 16px;
  border-radius: 45px;
  cursor: pointer;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(240, 79, 61, 0.3);
  transition: all 0.2s;
}

.clear-chat-button:hover {
  background-color: #E03A2A;
  box-shadow: 0 3px 6px rgba(240, 79, 61, 0.4);
}

.clear-chat-button:disabled {
  background-color: #f0a39a;
  cursor: not-allowed;
  box-shadow: none;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
