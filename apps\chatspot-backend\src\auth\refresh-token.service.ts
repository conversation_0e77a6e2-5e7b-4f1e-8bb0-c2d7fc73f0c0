import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, LessThan, MoreThan } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { RefreshToken } from './refresh-token.entity';
import { randomBytes } from 'crypto';

@Injectable()
export class RefreshTokenService {
  constructor(
    @InjectRepository(RefreshToken)
    private refreshTokenRepo: Repository<RefreshToken>,
    private configService: ConfigService,
  ) {}

  /**
   * Generate a new refresh token for a user
   */
  async generateRefreshToken(
    userId: string,
    deviceInfo?: { deviceId?: string; userAgent?: string; ipAddress?: string }
  ): Promise<RefreshToken> {
    // Generate a secure random token
    const token = randomBytes(64).toString('hex');

    // Calculate expiration date (7 days from now)
    const expirationDays = this.parseExpirationTime(
      this.configService.get('JWT_REFRESH_EXPIRATION', '7d')
    );
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + expirationDays);

    // Create and save the refresh token
    const refreshToken = this.refreshTokenRepo.create({
      token,
      userId,
      expiresAt,
      isRevoked: false,
      deviceId: deviceInfo?.deviceId,
      userAgent: deviceInfo?.userAgent,
      ipAddress: deviceInfo?.ipAddress,
    });

    return this.refreshTokenRepo.save(refreshToken);
  }

  /**
   * Validate a refresh token
   */
  async validateRefreshToken(token: string): Promise<RefreshToken | null> {
    const refreshToken = await this.refreshTokenRepo.findOne({
      where: { token, isRevoked: false },
      relations: ['user'],
    });

    if (!refreshToken) {
      return null;
    }

    // Check if token is expired
    if (refreshToken.expiresAt < new Date()) {
      // Mark as revoked and return null
      await this.revokeRefreshToken(token);
      return null;
    }

    return refreshToken;
  }

  /**
   * Revoke a refresh token
   */
  async revokeRefreshToken(token: string): Promise<void> {
    await this.refreshTokenRepo.update(
      { token },
      { isRevoked: true }
    );
  }

  /**
   * Revoke all refresh tokens for a user
   */
  async revokeAllUserTokens(userId: string): Promise<void> {
    await this.refreshTokenRepo.update(
      { userId, isRevoked: false },
      { isRevoked: true }
    );
  }

  /**
   * Check if a user has any valid (non-revoked, non-expired) refresh tokens
   * Used by JWT strategy to validate if session is still active
   */
  async hasValidTokens(userId: string): Promise<boolean> {
    const validTokenCount = await this.refreshTokenRepo.count({
      where: {
        userId,
        isRevoked: false,
        expiresAt: MoreThan(new Date())
      }
    });

    return validTokenCount > 0;
  }

  /**
   * Rotate refresh token (revoke old and create new)
   */
  async rotateRefreshToken(oldToken: string, userId: string): Promise<RefreshToken> {
    // Revoke the old token
    await this.revokeRefreshToken(oldToken);
    
    // Generate a new token
    return this.generateRefreshToken(userId);
  }

  /**
   * Clean up expired tokens (should be run periodically)
   */
  async cleanupExpiredTokens(): Promise<void> {
    await this.refreshTokenRepo.delete({
      expiresAt: LessThan(new Date()),
    });
  }

  /**
   * Parse expiration time string (e.g., "7d", "24h", "30m")
   */
  private parseExpirationTime(expiration: string): number {
    const unit = expiration.slice(-1);
    const value = parseInt(expiration.slice(0, -1));

    switch (unit) {
      case 'd':
        return value; // days
      case 'h':
        return value / 24; // hours to days
      case 'm':
        return value / (24 * 60); // minutes to days
      default:
        return 7; // default to 7 days
    }
  }
}
