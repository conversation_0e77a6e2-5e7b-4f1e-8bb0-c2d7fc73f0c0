import AudioRecorderPlayer, {
  AVEncoderAudioQualityIOSType,
  AudioEncoderAndroidType,
  AudioSourceAndroidType,
  OutputFormatAndroidType,
} from 'react-native-audio-recorder-player';
import { Platform, PermissionsAndroid, Alert } from 'react-native';
import RNFS from 'react-native-fs';

export interface VoiceRecordingResult {
  success: boolean;
  audioPath?: string;
  duration?: number;
  error?: string;
}

class VoiceRecordingService {
  private recordingPath: string = '';
  private isRecording: boolean = false;
  private recordingStartTime: number = 0;

  constructor() {
    console.log('VoiceRecordingService constructor called');
    console.log('AudioRecorderPlayer:', AudioRecorderPlayer);

    // AudioRecorderPlayer is now a singleton, no need to instantiate
    AudioRecorderPlayer.setSubscriptionDuration(100); // Update every 100ms
    console.log('AudioRecorderPlayer configured successfully');
  }

  /**
   * Request microphone permission
   */
  async requestMicrophonePermission(): Promise<boolean> {
    if (Platform.OS === 'android') {
      try {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,
          {
            title: 'Microphone Permission',
            message: 'This app needs access to your microphone to record voice messages.',
            buttonNeutral: 'Ask Me Later',
            buttonNegative: 'Cancel',
            buttonPositive: 'OK',
          }
        );
        return granted === PermissionsAndroid.RESULTS.GRANTED;
      } catch (err) {
        console.warn('Permission request error:', err);
        return false;
      }
    }
    return true; // iOS handles permissions automatically
  }

  /**
   * Start voice recording
   */
  async startRecording(onProgress?: (currentTime: number) => void): Promise<VoiceRecordingResult> {
    try {
      // Check permission
      const hasPermission = await this.requestMicrophonePermission();
      if (!hasPermission) {
        return { success: false, error: 'Microphone permission denied' };
      }

      // Generate unique filename
      const timestamp = Date.now();
      const fileName = `voice_${timestamp}.m4a`;
      this.recordingPath = `${RNFS.CachesDirectoryPath}/${fileName}`;

      // Configure recording options
      const audioSet = {
        AudioEncoderAndroid: AudioEncoderAndroidType.AAC,
        AudioSourceAndroid: AudioSourceAndroidType.MIC,
        AVEncoderAudioQualityKeyIOS: AVEncoderAudioQualityIOSType.high,
        AVNumberOfChannelsKeyIOS: 1,
        OutputFormatAndroid: OutputFormatAndroidType.AAC_ADTS,
      };

      // Start recording
      const result = await AudioRecorderPlayer.startRecorder(
        this.recordingPath,
        audioSet
      );

      this.isRecording = true;
      this.recordingStartTime = Date.now();

      // Set up progress listener
      if (onProgress) {
        AudioRecorderPlayer.addRecordBackListener((e: any) => {
          const currentTime = Math.floor(e.currentPosition / 1000); // Convert to seconds
          onProgress(currentTime);
        });
      }

      console.log('Recording started:', result);
      return { success: true, audioPath: this.recordingPath };
    } catch (error) {
      console.error('Error starting recording:', error);
      return { success: false, error: 'Failed to start recording' };
    }
  }

  /**
   * Stop voice recording
   */
  async stopRecording(): Promise<VoiceRecordingResult> {
    try {
      if (!this.isRecording) {
        return { success: false, error: 'No recording in progress' };
      }

      const result = await AudioRecorderPlayer.stopRecorder();
      AudioRecorderPlayer.removeRecordBackListener();
      
      this.isRecording = false;
      const duration = Math.floor((Date.now() - this.recordingStartTime) / 1000);

      // Check if file exists and has content
      const fileExists = await RNFS.exists(this.recordingPath);
      if (!fileExists) {
        return { success: false, error: 'Recording file not found' };
      }

      const fileStats = await RNFS.stat(this.recordingPath);
      if (fileStats.size === 0) {
        return { success: false, error: 'Recording file is empty' };
      }

      console.log('Recording stopped:', result);
      return { 
        success: true, 
        audioPath: this.recordingPath,
        duration: duration
      };
    } catch (error) {
      console.error('Error stopping recording:', error);
      return { success: false, error: 'Failed to stop recording' };
    }
  }

  /**
   * Cancel recording and delete file
   */
  async cancelRecording(): Promise<void> {
    try {
      if (this.isRecording) {
        await AudioRecorderPlayer.stopRecorder();
        AudioRecorderPlayer.removeRecordBackListener();
        this.isRecording = false;
      }

      // Delete the recording file
      if (this.recordingPath && await RNFS.exists(this.recordingPath)) {
        await RNFS.unlink(this.recordingPath);
      }

      this.recordingPath = '';
    } catch (error) {
      console.error('Error canceling recording:', error);
    }
  }

  /**
   * Play audio file
   */
  async playAudio(audioPath: string, onProgress?: (currentTime: number, duration: number) => void): Promise<void> {
    try {
      const result = await AudioRecorderPlayer.startPlayer(audioPath);

      if (onProgress) {
        AudioRecorderPlayer.addPlayBackListener((e: any) => {
          const currentTime = Math.floor(e.currentPosition / 1000);
          const duration = Math.floor(e.duration / 1000);
          onProgress(currentTime, duration);
        });
      }

      console.log('Audio playback started:', result);
    } catch (error) {
      console.error('Error playing audio:', error);
      Alert.alert('Error', 'Failed to play audio');
    }
  }

  /**
   * Stop audio playback
   */
  async stopPlayback(): Promise<void> {
    try {
      await AudioRecorderPlayer.stopPlayer();
      AudioRecorderPlayer.removePlayBackListener();
    } catch (error) {
      console.error('Error stopping playback:', error);
    }
  }

  /**
   * Pause audio playback
   */
  async pausePlayback(): Promise<void> {
    try {
      await AudioRecorderPlayer.pausePlayer();
    } catch (error) {
      console.error('Error pausing playback:', error);
    }
  }

  /**
   * Resume audio playback
   */
  async resumePlayback(): Promise<void> {
    try {
      await AudioRecorderPlayer.resumePlayer();
    } catch (error) {
      console.error('Error resuming playback:', error);
    }
  }

  /**
   * Get current recording status
   */
  isCurrentlyRecording(): boolean {
    return this.isRecording;
  }

  /**
   * Format time in MM:SS format
   */
  formatTime(seconds: number): string {
    return AudioRecorderPlayer.mmss(Math.floor(seconds));
  }

  /**
   * Clean up resources
   */
  cleanup(): void {
    AudioRecorderPlayer.removeRecordBackListener();
    AudioRecorderPlayer.removePlayBackListener();
  }
}

export const voiceRecordingService = new VoiceRecordingService();
