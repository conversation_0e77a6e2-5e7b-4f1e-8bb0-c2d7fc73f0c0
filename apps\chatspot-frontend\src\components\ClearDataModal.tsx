import React from 'react';
import './Modal.css';

interface ClearDataModalProps {
  onClose: () => void;
  onConfirm: () => void;
  loading: boolean;
}

const ClearDataModal: React.FC<ClearDataModalProps> = ({ onClose, onConfirm, loading }) => {
  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal-content" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h2>Clear All Data</h2>
          <button className="modal-close-button" onClick={onClose}>×</button>
        </div>

        <div className="modal-body">
          <p>Are you sure you want to clear all messages and room data?</p>
          <p className="warning-text">This action cannot be undone and will delete all your conversations.</p>
        </div>

        <div className="modal-actions">
          <button
            type="button"
            className="cancel-button"
            onClick={onClose}
            disabled={loading}
          >
            Cancel
          </button>
          <button
            type="button"
            className="clear-chat-button"
            onClick={onConfirm}
            disabled={loading}
          >
            {loading ? 'Clearing...' : 'Clear All Data'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ClearDataModal;
