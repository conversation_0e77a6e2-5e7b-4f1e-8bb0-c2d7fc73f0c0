import { useEffect, useState } from 'react';
import { Observable, Subscription } from 'rxjs';

/**
 * Hook to observe WatermelonDB observables and update React state
 * @param observable - The WatermelonDB observable to watch
 * @param initialValue - Initial value to use before the observable emits
 * @returns The current value from the observable
 */
export function useWatermelonObservable<T>(
  observable: Observable<T> | null,
  initialValue: T
): T {
  const [value, setValue] = useState<T>(initialValue);

  useEffect(() => {
    if (!observable) {
      setValue(initialValue);
      return;
    }

    let subscription: Subscription;

    try {
      subscription = observable.subscribe({
        next: (newValue) => {
          setValue(newValue);
        },
        error: (error) => {
          console.error('WatermelonDB observable error:', error);
          setValue(initialValue);
        }
      });
    } catch (error) {
      console.error('Failed to subscribe to WatermelonDB observable:', error);
      setValue(initialValue);
    }

    return () => {
      if (subscription) {
        subscription.unsubscribe();
      }
    };
  }, [observable, initialValue]);

  return value;
}
