import api from './api';

// Notification service for handling FCM tokens and notifications
export const notificationService = {
  // Register FCM token with backend
  registerToken: async (userId: string, token: string): Promise<boolean> => {
    try {
      const response = await api.post('/api/notifications/register-token', {
        userId,
        token,
      });
      return response.status === 200;
    } catch (error: any) {
      console.error('Failed to register FCM token:', error);
      return false;
    }
  },

  // Update notification settings
  updateSettings: async (userId: string, settings: any): Promise<boolean> => {
    try {
      const response = await api.post('/api/notifications/settings', {
        userId,
        settings,
      });
      return response.status === 200;
    } catch (error: any) {
      console.error('Failed to update notification settings:', error);
      return false;
    }
  },

  // Get notification settings
  getSettings: async (userId: string): Promise<any> => {
    try {
      const response = await api.get(`/api/notifications/settings/${userId}`);
      return response.data;
    } catch (error: any) {
      console.error('Failed to get notification settings:', error);
      return null;
    }
  },
};

export default notificationService;
