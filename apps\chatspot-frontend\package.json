{"name": "chatspot-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --mode development", "dev:staging": "vite --mode staging", "dev:prod": "vite --mode production", "build": "vite build --mode production", "build:dev": "vite build --mode development", "build:staging": "vite build --mode staging", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@nozbe/watermelondb": "^0.28.0", "@nozbe/with-observables": "^1.6.0", "@privacyresearch/libsignal-protocol-typescript": "^0.0.16", "@reduxjs/toolkit": "^2.7.0", "@types/lodash": "^4.17.16", "axios": "^1.8.4", "date-fns": "^4.1.0", "firebase": "^11.7.1", "lodash": "^4.17.21", "react": "^19.0.0", "react-dom": "^19.0.0", "react-redux": "^9.2.0", "react-router-dom": "^7.5.1", "redux": "^5.0.1", "redux-saga": "^1.3.0", "socket.io-client": "^4.8.1", "uuid": "^11.1.0"}, "devDependencies": {"@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-decorators": "^7.25.9", "@eslint/js": "^9.22.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "vite": "^6.3.3"}}