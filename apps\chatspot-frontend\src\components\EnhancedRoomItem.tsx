import React, { useState, useEffect } from 'react';
import { withObservables } from '@nozbe/watermelondb/react';
import { formatDistanceToNow } from 'date-fns';
import { useSelector } from 'react-redux';
import { selectUser } from '../redux/slices/authSlice';
import UserInfo from './UserInfo';
import { database } from '../database/config';

interface EnhancedRoomItemProps {
  onRoomSelect: (username: string) => void;
  selectedUsername: string | null;
  room?: any; // This will be injected by withObservables
}

// The base component that receives the observed room
const RoomItemBase: React.FC<EnhancedRoomItemProps> = ({
  room,
  onRoomSelect,
  selectedUsername
}) => {
  const currentUser = useSelector(selectUser);
  const [isMobileView, setIsMobileView] = useState<boolean>(window.innerWidth < 768);

  // Handle window resize to detect mobile/desktop view
  useEffect(() => {
    const handleResize = () => {
      setIsMobileView(window.innerWidth < 768);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  if (!room) {
    return null;
  }

  // Format timestamp to a readable date/time
  const formatTime = (timestamp: number): string => {
    if (!timestamp) return '';

    try {
      const date = new Date(timestamp);
      const now = new Date();
      const isToday = date.toDateString() === now.toDateString();

      if (isToday) {
        return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
      } else {
        // Use date-fns to get relative time like "2 days ago"
        return formatDistanceToNow(date, { addSuffix: true });
      }
    } catch (error) {
      return '';
    }
  };

  // Truncate long messages
  const truncateMessage = (message: string): string => {
    if (!message) return '';
    return message.length > 40 ? `${message.substring(0, 40)}...` : message;
  };

  // Check if there are unread messages
  const hasUnreadMessages = room.unreadCount > 0;

  // Check if this room is for the current user (receiver) and has unread messages
  // We show the unread indicator when:
  // 1. There are unread messages
  // 2. The room belongs to the current user (username === currentUser)
  const showUnreadIndicator = hasUnreadMessages;

  return (
    <li
      className={`room-item ${!isMobileView && room.username === selectedUsername ? 'selected' : ''} ${room.username === currentUser ? 'self-chat' : ''} ${showUnreadIndicator ? 'unread' : ''}`}
      onClick={() => room.username !== currentUser && onRoomSelect(room.username)}
    >
      <div className="room-details">
        <div className="room-header">
          <UserInfo
            userId={room.username}
            className="room-user-info"
            lastMessage={!showUnreadIndicator ? '' : truncateMessage(room.lastMsg)}
            disableEmoji={false} // Set to true to disable emoji visibility
          />
          <div className="room-info">
            {showUnreadIndicator && (
              <span className="unread-indicator">{room.unreadCount > 99 ? '99+' : room.unreadCount}</span>
            )}
            <span className="room-time">{formatTime(room.updated)}</span>
          </div>
        </div>
      </div>
    </li>
  );
};

// Enhance the component with withObservables to make it reactive
const enhance = withObservables(['room'], ({ room }) => ({
  room
}))


// Export the enhanced component
export default enhance(RoomItemBase);
