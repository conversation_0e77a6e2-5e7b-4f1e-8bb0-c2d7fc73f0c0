import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from './user.entity';
import { UserDto } from './dto';

@Injectable()
export class UsersService {
  constructor(
    @InjectRepository(User)
    private userRepo: Repository<User>,
  ) {}

  /**
   * Find a user by their username
   * @param username The username to search for
   * @returns The user's ID if found
   * @throws NotFoundException if the user is not found
   */
  async findUserIdByUsername(username: string): Promise<string> {
    const user = await this.userRepo.findOne({ where: { username } });
    if (!user) {
      throw new NotFoundException(`User with username "${username}" not found`);
    }
    return user.id;
  }

  /**
   * Find a username by their user ID
   * @param userId The user ID to search for
   * @returns The username if found
   * @throws NotFoundException if the user is not found
   */
  async findUsernameByUserId(userId: string): Promise<string> {
    const user = await this.userRepo.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException(`User with ID "${userId}" not found`);
    }
    return user.username;
  }

  /**
   * Find a user by their ID
   * @param userId The user ID to search for
   * @returns The user information if found
   * @throws NotFoundException if the user is not found
   */
  async findUserById(userId: string): Promise<UserDto> {
    const user = await this.userRepo.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException(`User with ID "${userId}" not found`);
    }

    // Convert to DTO to avoid exposing sensitive information
    const userDto: UserDto = {
      id: user.id,
      username: user.username,
      isAdmin: user.isAdmin,
    };

    return userDto;
  }
}
