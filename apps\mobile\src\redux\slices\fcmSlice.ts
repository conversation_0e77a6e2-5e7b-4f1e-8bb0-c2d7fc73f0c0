/**
 * FCM (Firebase Cloud Messaging) Redux Slice
 * 
 * Manages the state for push notifications including:
 * - FCM token management
 * - Notification permissions
 * - Registration status with backend
 */

import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { fcmService } from '../../services/fcmService';
import { debugLog } from '../../utils/env';

// State interface
interface FCMState {
  token: string | null;
  isInitialized: boolean;
  hasPermission: boolean;
  isRegisteredWithBackend: boolean;
  isLoading: boolean;
  error: string | null;
}

// Initial state
const initialState: FCMState = {
  token: null,
  isInitialized: false,
  hasPermission: false,
  isRegisteredWithBackend: false,
  isLoading: false,
  error: null,
};

// Async thunks
export const initializeFCM = createAsyncThunk(
  'fcm/initialize',
  async (_, { rejectWithValue }) => {
    try {
      debugLog('Initializing FCM...');
      const success = await fcmService.initialize();
      
      if (!success) {
        throw new Error('Failed to initialize FCM service');
      }

      const token = await fcmService.getFCMToken();
      return {
        token,
        isInitialized: true,
        hasPermission: true,
      };
    } catch (error: any) {
      debugLog('FCM initialization error:', error);
      return rejectWithValue(error.message || 'Failed to initialize FCM');
    }
  }
);

export const registerTokenWithBackend = createAsyncThunk(
  'fcm/registerToken',
  async (username: string, { rejectWithValue }) => {
    try {
      debugLog('Registering FCM token with backend for user:', username);
      const success = await fcmService.registerTokenWithBackend(username);
      
      if (!success) {
        throw new Error('Failed to register FCM token with backend');
      }

      return { isRegisteredWithBackend: true };
    } catch (error: any) {
      debugLog('FCM token registration error:', error);
      return rejectWithValue(error.message || 'Failed to register FCM token');
    }
  }
);

export const requestNotificationPermission = createAsyncThunk(
  'fcm/requestPermission',
  async (_, { rejectWithValue }) => {
    try {
      debugLog('Requesting notification permission...');
      const hasPermission = await fcmService.requestPermission();
      
      return { hasPermission };
    } catch (error: any) {
      debugLog('Permission request error:', error);
      return rejectWithValue(error.message || 'Failed to request notification permission');
    }
  }
);

// FCM slice
const fcmSlice = createSlice({
  name: 'fcm',
  initialState,
  reducers: {
    // Reset FCM state
    resetFCMState: (state) => {
      state.token = null;
      state.isInitialized = false;
      state.hasPermission = false;
      state.isRegisteredWithBackend = false;
      state.isLoading = false;
      state.error = null;
    },
    
    // Update FCM token (for token refresh)
    updateFCMToken: (state, action: PayloadAction<string>) => {
      state.token = action.payload;
      // Reset backend registration status when token changes
      state.isRegisteredWithBackend = false;
    },
    
    // Clear error
    clearFCMError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    // Initialize FCM
    builder
      .addCase(initializeFCM.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(initializeFCM.fulfilled, (state, action) => {
        state.isLoading = false;
        state.token = action.payload.token;
        state.isInitialized = action.payload.isInitialized;
        state.hasPermission = action.payload.hasPermission;
        state.error = null;
      })
      .addCase(initializeFCM.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Register token with backend
    builder
      .addCase(registerTokenWithBackend.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(registerTokenWithBackend.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isRegisteredWithBackend = action.payload.isRegisteredWithBackend;
        state.error = null;
      })
      .addCase(registerTokenWithBackend.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // Request notification permission
    builder
      .addCase(requestNotificationPermission.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(requestNotificationPermission.fulfilled, (state, action) => {
        state.isLoading = false;
        state.hasPermission = action.payload.hasPermission;
        state.error = null;
      })
      .addCase(requestNotificationPermission.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

// Export actions
export const { resetFCMState, updateFCMToken, clearFCMError } = fcmSlice.actions;

// Selectors
export const selectFCMToken = (state: { fcm: FCMState }) => state.fcm.token;
export const selectFCMInitialized = (state: { fcm: FCMState }) => state.fcm.isInitialized;
export const selectFCMPermission = (state: { fcm: FCMState }) => state.fcm.hasPermission;
export const selectFCMRegistered = (state: { fcm: FCMState }) => state.fcm.isRegisteredWithBackend;
export const selectFCMLoading = (state: { fcm: FCMState }) => state.fcm.isLoading;
export const selectFCMError = (state: { fcm: FCMState }) => state.fcm.error;

// Export reducer
export default fcmSlice.reducer;
