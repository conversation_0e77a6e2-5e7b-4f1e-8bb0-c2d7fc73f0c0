{"buildFiles": ["C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-bootsplash\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-image-picker\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-nitro-modules\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-safe-area-context\\android\\src\\main\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-screens\\android\\src\\main\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-svg\\android\\src\\main\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native-vector-icons\\android\\build\\generated\\source\\codegen\\jni\\CMakeLists.txt", "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\android\\app\\.cxx\\Debug\\5d6n2e2o\\x86_64", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\android\\app\\.cxx\\Debug\\5d6n2e2o\\x86_64", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"appmodules::@6890427a1f51a3e7e1df": {"toolchain": "toolchain", "abi": "x86_64", "artifactName": "appmodules", "output": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\android\\app\\build\\intermediates\\cxx\\Debug\\5d6n2e2o\\obj\\x86_64\\libappmodules.so", "runtimeFiles": ["C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\android\\app\\build\\intermediates\\cxx\\Debug\\5d6n2e2o\\obj\\x86_64\\libreact_codegen_safeareacontext.so", "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\android\\app\\build\\intermediates\\cxx\\Debug\\5d6n2e2o\\obj\\x86_64\\libreact_codegen_rnscreens.so", "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\android\\app\\build\\intermediates\\cxx\\Debug\\5d6n2e2o\\obj\\x86_64\\libreact_codegen_rnsvg.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\6e78c40f366fa67318455816b3ebd5e1\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.x86_64\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\d7fd0da1be39f0357ed881cbbe068c29\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\jsi\\libs\\android.x86_64\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\d7fd0da1be39f0357ed881cbbe068c29\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\reactnative\\libs\\android.x86_64\\libreactnative.so"]}, "react_codegen_NitroModulesSpec::@472a82f9c57d3d3b8644": {"toolchain": "toolchain", "abi": "x86_64", "artifactName": "react_codegen_NitroModulesSpec"}, "react_codegen_RNBootSplashSpec::@05d5bd8b08339ce1ebaa": {"toolchain": "toolchain", "abi": "x86_64", "artifactName": "react_codegen_RNBootSplashSpec"}, "react_codegen_RNImagePickerSpec::@f66ee9a2efecfb28bee4": {"toolchain": "toolchain", "abi": "x86_64", "artifactName": "react_codegen_RNImagePickerSpec"}, "react_codegen_RNVectorIconsSpec::@479809fae146501fd34d": {"toolchain": "toolchain", "abi": "x86_64", "artifactName": "react_codegen_RNVectorIconsSpec"}, "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe": {"toolchain": "toolchain", "abi": "x86_64", "artifactName": "react_codegen_rnasyncstorage"}, "react_codegen_rngesturehandler_codegen::@39f233abcd2c728bc6ec": {"toolchain": "toolchain", "abi": "x86_64", "artifactName": "react_codegen_rngesturehandler_codegen"}, "react_codegen_rnscreens::@25bcbd507e98d3a854ad": {"toolchain": "toolchain", "abi": "x86_64", "artifactName": "react_codegen_rnscreens", "output": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\android\\app\\build\\intermediates\\cxx\\Debug\\5d6n2e2o\\obj\\x86_64\\libreact_codegen_rnscreens.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\d7fd0da1be39f0357ed881cbbe068c29\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\reactnative\\libs\\android.x86_64\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\d7fd0da1be39f0357ed881cbbe068c29\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\jsi\\libs\\android.x86_64\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\6e78c40f366fa67318455816b3ebd5e1\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.x86_64\\libfbjni.so"]}, "react_codegen_rnsvg::@4f40eb209d0c0b4a3b65": {"toolchain": "toolchain", "abi": "x86_64", "artifactName": "react_codegen_rnsvg", "output": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\android\\app\\build\\intermediates\\cxx\\Debug\\5d6n2e2o\\obj\\x86_64\\libreact_codegen_rnsvg.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\d7fd0da1be39f0357ed881cbbe068c29\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\reactnative\\libs\\android.x86_64\\libreactnative.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\d7fd0da1be39f0357ed881cbbe068c29\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\jsi\\libs\\android.x86_64\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\6e78c40f366fa67318455816b3ebd5e1\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.x86_64\\libfbjni.so"]}, "react_codegen_safeareacontext::@7984cd80db47aa7b952a": {"toolchain": "toolchain", "abi": "x86_64", "artifactName": "react_codegen_safeareacontext", "output": "C:\\Users\\<USER>\\chatspot-messenger\\apps\\mobile\\android\\app\\build\\intermediates\\cxx\\Debug\\5d6n2e2o\\obj\\x86_64\\libreact_codegen_safeareacontext.so", "runtimeFiles": ["C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\6e78c40f366fa67318455816b3ebd5e1\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.x86_64\\libfbjni.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\d7fd0da1be39f0357ed881cbbe068c29\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\jsi\\libs\\android.x86_64\\libjsi.so", "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\d7fd0da1be39f0357ed881cbbe068c29\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\reactnative\\libs\\android.x86_64\\libreactnative.so"]}}, "toolchains": {"toolchain": {"cCompilerExecutable": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": ["cpp"]}