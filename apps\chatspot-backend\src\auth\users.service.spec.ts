import { Test, TestingModule } from '@nestjs/testing';
import { UsersService } from './users.service';
import { getRepositoryToken } from '@nestjs/typeorm';
import { User } from './user.entity';
import { NotFoundException } from '@nestjs/common';

describe('UsersService', () => {
  let service: UsersService;
  let mockUserRepository;

  beforeEach(async () => {
    mockUserRepository = {
      findOne: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UsersService,
        {
          provide: getRepositoryToken(User),
          useValue: mockUserRepository,
        },
      ],
    }).compile();

    service = module.get<UsersService>(UsersService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('findUserIdByUsername', () => {
    it('should return userId when username exists', async () => {
      const username = 'testuser';
      const mockUser = {
        id: '123e4567-e89b-12d3-a456-426614174000',
        username,
      };
      
      mockUserRepository.findOne.mockResolvedValue(mockUser);
      
      const result = await service.findUserIdByUsername(username);
      
      expect(result).toEqual(mockUser.id);
      expect(mockUserRepository.findOne).toHaveBeenCalledWith({ where: { username } });
    });

    it('should throw NotFoundException when username does not exist', async () => {
      const username = 'nonexistentuser';
      
      mockUserRepository.findOne.mockResolvedValue(null);
      
      await expect(service.findUserIdByUsername(username)).rejects.toThrow(
        new NotFoundException(`User with username "${username}" not found`),
      );
    });
  });

  describe('findUserById', () => {
    it('should return user when userId exists', async () => {
      const userId = '123e4567-e89b-12d3-a456-426614174000';
      const mockUser = {
        id: userId,
        username: 'testuser',
        password: 'hashedpassword',
      };
      
      mockUserRepository.findOne.mockResolvedValue(mockUser);
      
      const result = await service.findUserById(userId);
      
      expect(result).toEqual({
        id: mockUser.id,
        username: mockUser.username,
      });
      expect(mockUserRepository.findOne).toHaveBeenCalledWith({ where: { id: userId } });
    });

    it('should throw NotFoundException when userId does not exist', async () => {
      const userId = '123e4567-e89b-12d3-a456-426614174999';
      
      mockUserRepository.findOne.mockResolvedValue(null);
      
      await expect(service.findUserById(userId)).rejects.toThrow(
        new NotFoundException(`User with ID "${userId}" not found`),
      );
    });
  });
});
