services:
  - type: web
    name: chatspot-backend
    env: node
    buildCommand: npm install && npm run build
    startCommand: npm run start:prod
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: 3000
      - key: JWT_SECRET
        generateValue: true
      - key: JWT_EXPIRATION
        value: 1d
      - key: CORS_ORIGIN
        value: "*"
      - key: DATABASE_URL
        fromDatabase:
          name: chatspot-db
          property: connectionString

databases:
  - name: chatspot-db
    databaseName: chatspot
    user: chatspot_user
    plan: free
