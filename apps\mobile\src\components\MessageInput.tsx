import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Text,
  Alert,
  Image,
} from 'react-native';
import { useAppDispatch, useAppSelector } from '../hooks/redux';
import { sendMessageRequest, selectConnected, selectError } from '../redux/slices/socketSlice';
import { selectEmojiReactionsEnabled } from '../redux/slices/emojiReactionSlice';
import EmojiBar from './EmojiBar';
import { useTheme, radius } from '../theme';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { mediaUploadService } from '../services/mediaUploadService';
import MediaPickerModal from './MediaPickerModal';
import { voiceRecordingService } from '../services/voiceRecordingService';
import RNFS from 'react-native-fs';


interface MessageInputProps {
  receiverUsername: string;
  onFocus?: () => void;
  onBlur?: () => void;
}

const MessageInput: React.FC<MessageInputProps> = ({
  receiverUsername,
  onFocus,
  onBlur
}) => {
  const dispatch = useAppDispatch();
  const connected = useAppSelector(selectConnected);
  const error = useAppSelector(selectError);
  const emojiReactionsEnabled = useAppSelector(selectEmojiReactionsEnabled);
  const { colors } = useTheme();

  const [message, setMessage] = useState<string>('');
  const [sendStatus, setSendStatus] = useState<{ success: boolean, message: string } | null>(null);
  const inputRef = useRef<TextInput>(null);
  const [pendingMedia, setPendingMedia] = useState<any>(null);
  const [showMediaPicker, setShowMediaPicker] = useState<boolean>(false);
  const [isRecording, setIsRecording] = useState<boolean>(false);
  const [recordingTime, setRecordingTime] = useState<number>(0);
  const [recordedAudio, setRecordedAudio] = useState<{ path: string; duration: number } | null>(null);
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const recordingTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Watch for errors from Redux
  useEffect(() => {
    if (error && error.includes('Failed to send message')) {
      setSendStatus({
        success: false,
        message: error
      });
    }
  }, [error]);



  // Clear send status after 3 seconds
  useEffect(() => {
    if (sendStatus) {
      const timer = setTimeout(() => {
        setSendStatus(null);
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [sendStatus]);

  const handleSubmit = () => {
    if ((!message.trim() && !pendingMedia) || !receiverUsername || !connected) {
      if (!connected) {
        setSendStatus({
          success: false,
          message: 'Not connected to server'
        });
      }
      return;
    }

    // Send as a text+media message if pendingMedia is set
    dispatch(sendMessageRequest({
      receiverUsername,
      messageText: message.trim(),
      messageType: pendingMedia ? 'media' : 'text',
      mediaData: pendingMedia || undefined
    }));
    setMessage('');
    setPendingMedia(null);

    // Focus back on the input after sending
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  // Typing indicator state
  const [isTyping, setIsTyping] = useState<boolean>(false);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Send typing indicator
  const sendTypingIndicator = (typing: boolean) => {
    if (connected && receiverUsername) {
      dispatch(sendMessageRequest({
        receiverUsername,
        messageText: typing ? 'typing' : 'stopped_typing',
        messageType: 'typing'
      }));
    }
  };

  // Handle text input change and typing indicator
  const handleTextChange = (text: string) => {
    setMessage(text);

    // Handle typing indicator
    if (text.trim() && !isTyping) {
      // User started typing
      setIsTyping(true);
      sendTypingIndicator(true);
    } else if (!text.trim() && isTyping) {
      // User stopped typing
      setIsTyping(false);
      sendTypingIndicator(false);
    }

    // Reset typing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Set a timeout to stop the typing indicator after 3 seconds of inactivity
    typingTimeoutRef.current = setTimeout(() => {
      if (isTyping) {
        setIsTyping(false);
        sendTypingIndicator(false);
      }
    }, 3000);
  };

  // Clean up typing indicator on unmount
  useEffect(() => {
    return () => {
      // Clear typing timeout and send stopped typing on unmount
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }

      if (isTyping && connected && receiverUsername) {
        sendTypingIndicator(false);
      }
    };
  }, [isTyping, connected, receiverUsername]);

  // Handle emoji click
  const handleEmojiClick = (emoji: string) => {
    // Currently empty in frontend, could be used for inserting emoji into text
  };

  // Handle emoji long press
  const handleEmojiLongPress = (emoji: string) => {
    if (connected && receiverUsername) {
      // Get the mood name from the emoji
      let mood = '';
      switch (emoji) {
        case '😊': mood = 'happy'; break;
        case '😂': mood = 'laughing'; break;
        case '😡': mood = 'angry'; break;
        case '😢': mood = 'sad'; break;
        case '❤️': mood = 'love'; break;
        case '👍': mood = 'thumbsUp'; break;
        default: mood = 'feeling';
      }

      // Send emoji reaction message with emoji and mood
      dispatch(sendMessageRequest({
        receiverUsername,
        messageText: `${emoji}:${mood}`,
        messageType: 'emoji_reaction'
      }));
    }
  };

  // Handle emoji release
  const handleEmojiRelease = () => {
    if (connected && receiverUsername) {
      // Send message to stop showing emoji reaction
      dispatch(sendMessageRequest({
        receiverUsername,
        messageText: 'stopped_reaction',
        messageType: 'emoji_reaction'
      }));
    }
  };

  // Handle media upload - show modal
  const handleMediaUpload = () => {
    if (!connected || !receiverUsername) {
      setSendStatus({
        success: false,
        message: 'Not connected to server'
      });
      return;
    }

    setShowMediaPicker(true);
  };

  // Handle individual media type selections
  const handleMediaSelection = async (mediaType: 'image' | 'video' | 'document') => {
    setShowMediaPicker(false);

    if (!connected || !receiverUsername) {
      setSendStatus({
        success: false,
        message: 'Not connected to server'
      });
      return;
    }

    try {
      let result;
      switch (mediaType) {
        case 'image':
          result = await mediaUploadService.showPhotoOptions();
          break;
        case 'video':
          result = await mediaUploadService.showVideoOptions();
          break;
        case 'document':
          result = await mediaUploadService.pickDocument();
          break;
        default:
          result = { success: false, error: 'Invalid media type' };
      }

      if (result.success && result.file) {
        // Check if file type is supported
        if (!mediaUploadService.isSupportedFileType(result.file.type)) {
          setSendStatus({
            success: false,
            message: 'File type not supported'
          });
          return;
        }

        // Check file size based on media type
        let maxSize: number;
        let sizeLabel: string;

        if (result.file.type.startsWith('video/')) {
          maxSize = 100 * 1024 * 1024; // 100MB for videos
          sizeLabel = '100MB';
        } else if (result.file.type.startsWith('audio/')) {
          maxSize = 50 * 1024 * 1024; // 50MB for audio
          sizeLabel = '50MB';
        } else {
          maxSize = 10 * 1024 * 1024; // 10MB for images and other files
          sizeLabel = '10MB';
        }

        if (result.file.size > maxSize) {
          setSendStatus({
            success: false,
            message: `File size too large (max ${sizeLabel})`
          });
          return;
        }

        // Set pending media for preview and caption
        setPendingMedia(result.file);
      } else if (result.error && result.error !== 'User cancelled') {
        setSendStatus({
          success: false,
          message: result.error
        });
      }
    } catch (error) {
      console.error('Media selection error:', error);
      setSendStatus({
        success: false,
        message: 'Failed to select media'
      });
    }
  };

  const handleCloseMediaPicker = () => {
    setShowMediaPicker(false);
  };

  // Voice recording handlers
  const handleStartVoiceRecording = async () => {
    try {
      setIsRecording(true);
      setRecordingTime(0);

      // Start a backup timer in case AudioRecorderPlayer listener doesn't work
      const startTime = Date.now();
      recordingTimerRef.current = setInterval(() => {
        const elapsed = Math.floor((Date.now() - startTime) / 1000);
        setRecordingTime(elapsed);
      }, 1000);

      const result = await voiceRecordingService.startRecording((currentTime) => {
        // If the AudioRecorderPlayer listener works, use its time instead
        setRecordingTime(currentTime);
      });

      if (!result.success) {
        setIsRecording(false);
        if (recordingTimerRef.current) {
          clearInterval(recordingTimerRef.current);
          recordingTimerRef.current = null;
        }
        setSendStatus({
          success: false,
          message: result.error || 'Failed to start recording'
        });
      }
    } catch (error) {
      console.error('Voice recording error:', error);
      setIsRecording(false);
      if (recordingTimerRef.current) {
        clearInterval(recordingTimerRef.current);
        recordingTimerRef.current = null;
      }
      setSendStatus({
        success: false,
        message: 'Voice recording service unavailable. Please restart the app.'
      });
    }
  };

  const handleStopVoiceRecording = async () => {
    try {
      const result = await voiceRecordingService.stopRecording();
      setIsRecording(false);
      setRecordingTime(0);

      // Clear the backup timer
      if (recordingTimerRef.current) {
        clearInterval(recordingTimerRef.current);
        recordingTimerRef.current = null;
      }

      if (result.success && result.audioPath && result.duration) {
        // Set the recorded audio as pending media
        setRecordedAudio({
          path: result.audioPath,
          duration: result.duration
        });

        // Get file size for the recorded audio
        const fileStats = await RNFS.stat(result.audioPath);

        // Create a media file object for uploading
        // Ensure the URI has the correct format for FormData
        const audioUri = result.audioPath.startsWith('file://')
          ? result.audioPath
          : `file://${result.audioPath}`;

        const audioFile = {
          uri: audioUri,
          name: `voice_${Date.now()}.m4a`,
          type: 'audio/m4a',
          size: fileStats.size,
        };

        console.log('audioFile', audioFile)

        try {
          // Upload the audio file to backend first (like other media types)
          console.log('Uploading audio file:', audioFile);
          setIsUploading(true);
          const uploadResult = await mediaUploadService.uploadAudioFile(audioFile);
          setIsUploading(false);
          console.log('Audio upload result:', uploadResult);

          if (uploadResult.success && uploadResult.file) {
            setPendingMedia(uploadResult.file);
          } else {
            setSendStatus({
              success: false,
              message: uploadResult.error || 'Failed to upload audio recording'
            });
          }
        } catch (uploadError) {
          console.error('Error uploading recorded audio:', uploadError);
          setIsUploading(false);
          setSendStatus({
            success: false,
            message: 'Failed to upload audio recording'
          });
        }
      } else {
        setSendStatus({
          success: false,
          message: result.error || 'Failed to save recording'
        });
      }
    } catch (error) {
      console.error('Stop recording error:', error);
      setIsRecording(false);
      setSendStatus({
        success: false,
        message: 'Failed to stop recording'
      });
    }
  };

  const handleCancelVoiceRecording = async () => {
    try {
      await voiceRecordingService.cancelRecording();
      setIsRecording(false);
      setRecordingTime(0);
      setRecordedAudio(null);

      // Clear the backup timer
      if (recordingTimerRef.current) {
        clearInterval(recordingTimerRef.current);
        recordingTimerRef.current = null;
      }
    } catch (error) {
      console.error('Cancel recording error:', error);
    }
  };

  const styles = createStyles(colors);

  return (
    <View style={styles.container}>
      {sendStatus && (
        <View style={[
          styles.sendStatus,
          sendStatus.success ? styles.successStatus : styles.errorStatus
        ]}>
          <Text style={styles.statusText}>{sendStatus.message}</Text>
        </View>
      )}

      {/* Emoji Bar */}
      {emojiReactionsEnabled && (
        <EmojiBar
          onEmojiClick={handleEmojiClick}
          onEmojiLongPress={handleEmojiLongPress}
          onEmojiRelease={handleEmojiRelease}
        />
      )}

      {/* Recording Indicator */}
      {isRecording && (
        <View style={styles.recordingIndicator}>
          <View style={styles.recordingDot} />
          <Text style={styles.recordingText}>
            Recording... {voiceRecordingService.formatTime(recordingTime)}
          </Text>
          <TouchableOpacity onPress={handleCancelVoiceRecording} style={styles.cancelRecordingButton}>
            <Text style={styles.cancelRecordingText}>Cancel</Text>
          </TouchableOpacity>
        </View>
      )}

      {/* Media Preview */}
      {pendingMedia && !isRecording && (
        <View style={{ flexDirection: 'row', alignItems: 'center', marginHorizontal: 16, marginBottom: 8 }}>
          {pendingMedia.type.startsWith('image/') ? (
            <Image source={{ uri: pendingMedia.uri }} style={{ width: 60, height: 60, borderRadius: 8, marginRight: 8 }} />
          ) : pendingMedia.type.startsWith('video/') ? (
            <View style={{ width: 60, height: 60, borderRadius: 8, marginRight: 8, backgroundColor: colors.gray100, justifyContent: 'center', alignItems: 'center' }}>
              <Icon name="videocam" size={24} color={colors.primary} />
            </View>
          ) : pendingMedia.type.startsWith('audio/') ? (
            <View style={{ width: 60, height: 60, borderRadius: 8, marginRight: 8, backgroundColor: colors.gray100, justifyContent: 'center', alignItems: 'center' }}>
              <Icon name="audiotrack" size={24} color={colors.primary} />
            </View>
          ) : (
            <Icon name="insert-drive-file" size={48} color={colors.primary} style={{ marginRight: 8 }} />
          )}
          <Text numberOfLines={1} style={{ flex: 1 }}>{pendingMedia.name}</Text>
          <TouchableOpacity onPress={() => setPendingMedia(null)} style={{ marginLeft: 8 }}>
            <Icon name="close" size={24} color={colors.danger} />
          </TouchableOpacity>
        </View>
      )}

      <View style={styles.inputContainer}>
        {/* Media Upload Button */}
        <TouchableOpacity
          style={[
            styles.mediaButton,
            (!connected || !receiverUsername) && styles.disabledButton
          ]}
          onPress={handleMediaUpload}
          disabled={!connected || !receiverUsername}
        >
          <Icon name="attach-file" style={styles.mediaButtonText} />
        </TouchableOpacity>

        <TextInput
          ref={inputRef}
          style={styles.textInput}
          value={message}
          onChangeText={handleTextChange}
          placeholder={connected ? (pendingMedia ? 'Add a caption...' : 'Type a message...') : 'Connect to send messages'}
          editable={connected && !!receiverUsername}
          placeholderTextColor={colors.toneLight1}
          multiline
          onFocus={onFocus}
          onBlur={onBlur}
          returnKeyType="send"
          onSubmitEditing={handleSubmit}
        />
        {/* Send Button or Mic Button */}
        {connected && receiverUsername && (message.trim() || pendingMedia) ? (
          // Show Send Button when there's content to send
          <TouchableOpacity
            style={styles.sendButton}
            onPress={handleSubmit}
          >
            <Icon name="arrow-back" style={[styles.sendButtonText, { transform: [{ rotate: '135deg' }] }]} />
          </TouchableOpacity>
        ) : (
          // Show Mic Button when no content to send
          <TouchableOpacity
            style={[
              styles.micButton,
              isRecording && styles.recordingButton,
              (!connected || !receiverUsername) && styles.disabledButton
            ]}
            onPress={isRecording ? handleStopVoiceRecording : handleStartVoiceRecording}
            disabled={!connected || !receiverUsername}
            onLongPress={isRecording ? handleCancelVoiceRecording : undefined}
          >
            <Icon
              name={isRecording ? "stop" : "mic"}
              style={[
                styles.micButtonText,
                isRecording && styles.recordingButtonText
              ]}
            />
          </TouchableOpacity>
        )}
      </View>

      {/* Media Picker Modal */}
      <MediaPickerModal
        isVisible={showMediaPicker}
        onClose={handleCloseMediaPicker}
        onSelectImage={() => handleMediaSelection('image')}
        onSelectVideo={() => handleMediaSelection('video')}
        onSelectDocument={() => handleMediaSelection('document')}
      />
    </View>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  container: {
    backgroundColor: colors.cardBackground,
  },
  sendStatus: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  successStatus: {
    backgroundColor: colors.success,
  },
  errorStatus: {
    backgroundColor: colors.danger,
  },
  statusText: {
    color: colors.white,
    fontSize: 14,
    fontFamily: 'Outfit-Regular',
    textAlign: 'center',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  textInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: colors.border,
    borderRadius: radius.round,
    paddingHorizontal: 16,
    paddingVertical: 10,
    marginHorizontal: 8,
    maxHeight: 100,
    fontSize: 14,
    fontFamily: 'Outfit-Regular',
    color: colors.text,
  },
  sendButton: {
    backgroundColor: colors.primary,
    borderRadius: radius.round,
    paddingHorizontal: 10,
    paddingVertical: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  disabledButton: {
    backgroundColor: colors.toneLight2,
  },
  mediaButton: {
    backgroundColor: colors.secondary,
    borderRadius: radius.round,
    paddingHorizontal: 10,
    paddingVertical: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  mediaButtonText: {
    color: colors.white,
    fontSize: 24,
    fontFamily: 'Outfit-Medium',
  },
  sendButtonText: {
    color: colors.white,
    fontSize: 24,
    fontFamily: 'Outfit-Medium',
  },
  micButton: {
    backgroundColor: colors.primary,
    borderRadius: radius.round,
    paddingHorizontal: 10,
    paddingVertical: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  recordingButton: {
    backgroundColor: colors.danger,
  },
  micButtonText: {
    color: colors.white,
    fontSize: 24,
    fontFamily: 'Outfit-Medium',
  },
  recordingButtonText: {
    color: colors.white,
  },
  recordingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: colors.danger + '20', // 20% opacity
    borderRadius: 8,
    marginHorizontal: 16,
    marginBottom: 8,
  },
  recordingDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: colors.danger,
    marginRight: 8,
  },
  recordingText: {
    flex: 1,
    fontSize: 14,
    fontFamily: 'Outfit-Medium',
    color: colors.danger,
  },
  cancelRecordingButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: colors.gray100,
    borderRadius: 4,
  },
  cancelRecordingText: {
    fontSize: 12,
    fontFamily: 'Outfit-Medium',
    color: colors.textSecondary,
  },
});

export default MessageInput;
