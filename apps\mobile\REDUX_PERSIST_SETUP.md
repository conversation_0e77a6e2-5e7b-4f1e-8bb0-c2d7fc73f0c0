# Redux Persist Setup

This document explains the Redux Persist implementation that replaces manual state hydration.

## Overview

Redux Persist automatically persists and rehydrates Redux state to/from AsyncStorage, eliminating the need for manual state management.

## Configuration

### Store Setup (`src/redux/store.ts`)

- **Persist Configuration**: Only `auth` and `fcm` states are persisted
- **Blacklisted States**: `chatDB`, `socket`, `typing`, `emojiReaction` are not persisted
- **Storage**: Uses AsyncStorage for persistence
- **Serializable Check**: Configured to ignore Redux Persist actions

### App Setup (`App.tsx`)

- **PersistGate**: Wraps the app content to ensure state is rehydrated before rendering
- **Loading Component**: Shows `LoadingScreen` while state is being rehydrated
- **Removed Manual Hydration**: No more manual `initializeAuth` calls

## Changes Made

### Removed Manual Storage Operations

1. **Auth Saga** (`src/redux/slices/authSlice.ts`):
   - Removed `initializeAuth` action
   - Removed manual storage operations from login/register sagas

2. **API Service** (`src/services/api.ts`):
   - Updated to accept tokens as parameters instead of reading from storage
   - Created factory functions for API instances with token refresh support

3. **Token Refresh Service** (`src/services/tokenRefreshService.ts`):
   - Updated to accept refresh token as parameter
   - Returns both access and refresh tokens for Redux state updates

4. **Socket Saga** (`src/redux/sagas/socketSaga.ts`):
   - Removed manual storage clear operations

### Persisted States

- **Auth State**: `user`, `token`, `refreshToken`, `isAuthenticated`, `loading`, `error`
- **FCM State**: FCM initialization and token registration state

### Non-Persisted States

- **ChatDB**: Local database state (managed by WatermelonDB)
- **Socket**: Connection state (recreated on app start)
- **Typing**: Real-time typing indicators
- **EmojiReaction**: Real-time emoji reactions

## Benefits

1. **Automatic State Management**: No more manual hydration logic
2. **Consistent State**: State is automatically restored on app restart
3. **Better Performance**: State is loaded once during app initialization
4. **Cleaner Code**: Removed manual storage operations throughout the app
5. **Type Safety**: Better TypeScript support with Redux Toolkit

## Usage

The app now automatically:
- Persists auth state when user logs in
- Restores auth state when app restarts
- Clears persisted state when user logs out
- Shows loading screen while state is being rehydrated

No additional code changes are needed - Redux Persist handles everything automatically. 