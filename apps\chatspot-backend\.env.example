# Application
PORT=3000
NODE_ENV=production

# Database
DATABASE_URL=postgres://user:password@localhost:5432/dbname
# Or individual connection parameters
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=chatuser
DB_PASSWORD=chatpassword
DB_DATABASE=chatdb

# JWT
JWT_SECRET=your_jwt_secret
JWT_EXPIRATION=1d

# CORS
CORS_ORIGIN=*

# S3 Storage Configuration
USE_S3_STORAGE=false
AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key
AWS_REGION=us-east-1
AWS_S3_BUCKET=your-s3-bucket-name
