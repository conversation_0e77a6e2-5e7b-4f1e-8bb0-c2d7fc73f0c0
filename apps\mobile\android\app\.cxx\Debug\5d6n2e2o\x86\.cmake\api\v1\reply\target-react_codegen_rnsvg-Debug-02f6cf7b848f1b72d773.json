{"artifacts": [{"path": "C:/Users/<USER>/chatspot-messenger/apps/mobile/android/app/build/intermediates/cxx/Debug/5d6n2e2o/obj/x86/libreact_codegen_rnsvg.so"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_link_libraries", "add_compile_options", "target_compile_options", "target_include_directories"], "files": ["C:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 28, "parent": 0}, {"command": 1, "file": 0, "line": 47, "parent": 0}, {"command": 1, "file": 0, "line": 74, "parent": 0}, {"command": 2, "file": 0, "line": 15, "parent": 0}, {"command": 3, "file": 0, "line": 79, "parent": 0}, {"command": 4, "file": 0, "line": 35, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC"}, {"backtrace": 4, "fragment": "-fexceptions"}, {"backtrace": 4, "fragment": "-frtti"}, {"backtrace": 4, "fragment": "-std=c++20"}, {"backtrace": 4, "fragment": "-Wall"}, {"backtrace": 4, "fragment": "-Wpedantic"}, {"backtrace": 4, "fragment": "-Wno-gnu-zero-variadic-macro-arguments"}, {"backtrace": 4, "fragment": "-Wno-dollar-in-identifier-extension"}, {"backtrace": 5, "fragment": "-DLOG_TAG=\\\"ReactNative\\\""}, {"backtrace": 0, "fragment": "-DFOLLY_NO_CONFIG=1"}, {"backtrace": 0, "fragment": "-DFOLLY_HAVE_CLOCK_GETTIME=1"}, {"backtrace": 0, "fragment": "-DFOLLY_USE_LIBCPP=1"}, {"backtrace": 0, "fragment": "-DFOLLY_CFG_NO_COROUTINES=1"}, {"backtrace": 0, "fragment": "-DFOLLY_MOBILE=1"}, {"backtrace": 0, "fragment": "-DFOLLY_HAVE_RECVMMSG=1"}, {"backtrace": 0, "fragment": "-DFOLLY_HAVE_PTHREAD=1"}, {"backtrace": 0, "fragment": "-DFOLLY_HAVE_XSI_STRERROR_R=1"}], "defines": [{"define": "react_codegen_rnsvg_EXPORTS"}], "includes": [{"backtrace": 6, "path": "C:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/."}, {"backtrace": 6, "path": "C:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp"}, {"backtrace": 6, "path": "C:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni"}, {"backtrace": 6, "path": "C:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg"}, {"backtrace": 2, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/reactnative/include"}, {"backtrace": 2, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.14.2/transforms/d7fd0da1be39f0357ed881cbbe068c29/transformed/react-android-0.79.2-debug/prefab/modules/jsi/include"}, {"backtrace": 3, "isSystem": true, "path": "C:/Users/<USER>/.gradle/caches/8.14.2/transforms/6e78c40f366fa67318455816b3ebd5e1/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"}], "language": "CXX", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10], "sysroot": {"path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}], "id": "react_codegen_rnsvg::@4f40eb209d0c0b4a3b65", "link": {"commandFragments": [{"fragment": "-Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments", "role": "flags"}, {"backtrace": 2, "fragment": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\d7fd0da1be39f0357ed881cbbe068c29\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\reactnative\\libs\\android.x86\\libreactnative.so", "role": "libraries"}, {"backtrace": 2, "fragment": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\d7fd0da1be39f0357ed881cbbe068c29\\transformed\\react-android-0.79.2-debug\\prefab\\modules\\jsi\\libs\\android.x86\\libjsi.so", "role": "libraries"}, {"backtrace": 3, "fragment": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.2\\transforms\\6e78c40f366fa67318455816b3ebd5e1\\transformed\\fbjni-0.7.0\\prefab\\modules\\fbjni\\libs\\android.x86\\libfbjni.so", "role": "libraries"}, {"fragment": "-latomic -lm", "role": "libraries"}], "language": "CXX", "sysroot": {"path": "C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot"}}, "name": "react_codegen_rnsvg", "nameOnDisk": "libreact_codegen_rnsvg.so", "paths": {"build": "rnsvg_autolinked_build", "source": "C:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni"}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "C:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGImageShadowNode.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGImageState.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGLayoutableShadowNode.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGShadowNodes.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/src/main/jni/rnsvg.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/ComponentDescriptors.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/Props.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/ShadowNodes.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/States.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "C:/Users/<USER>/chatspot-messenger/apps/mobile/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/rnsvgJSI-generated.cpp", "sourceGroupIndex": 0}], "type": "SHARED_LIBRARY"}