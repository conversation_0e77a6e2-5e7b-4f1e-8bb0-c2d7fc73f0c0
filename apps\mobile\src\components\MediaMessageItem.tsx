import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  Linking,
  ActivityIndicator,
  Modal,
} from 'react-native';
import { Image } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons'; // Note the updated icon set
import { useTheme, radius, spacing } from '../theme';
import { useDispatch, useSelector } from 'react-redux';
import { mediaDownloadRequested } from '../redux/slices/mediaSlice';
import { RootState } from '../redux/store';
import ImageViewing from 'react-native-image-viewing';
import Video from 'react-native-video';
import VoiceMessagePlayer from './VoiceMessagePlayer';


interface MediaMessageItemProps {
  message: {
    id: string;
    message: string;
    media_id?: string;
    media_uri?: string;
    media_name?: string;
    media_type?: string;
    media_size?: number;
    timestamp: number;
    is_mine?: boolean;
    isMine?: boolean;
    status: string;
  };
  formatTime: (timestamp: number) => string;
  isLastInGroup?: boolean;
}

const MediaMessageItem: React.FC<MediaMessageItemProps> = ({
  message,
  formatTime,
  isLastInGroup
}) => {
  const { colors } = useTheme();
  const dispatch = useDispatch();
  const mediaState = useSelector((state: RootState) =>
    message.media_id ? state.media.byId[message.media_id] : undefined
  );
  const [imageError, setImageError] = useState(false);
  const [imageLoading, setImageLoading] = useState(true);
  const [isViewerVisible, setViewerVisible] = useState(false);
  const [isVideoPlayerVisible, setVideoPlayerVisible] = useState(false);
  const [videoPaused, setVideoPaused] = useState(true);

  const isMine = message.isMine || message.is_mine;
  const isImage = mediaState?.type?.startsWith('image/') || message.media_type?.startsWith('image/');
  const isDocument = false;

  // Auto-download effect
  useEffect(() => {
    if (message.media_id && (!mediaState || mediaState.status === 'failure')) {
      dispatch(mediaDownloadRequested({ mediaId: message.media_id }));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [message.media_id]);

  const handleMediaPress = async () => {
    if (isImage) {
      setViewerVisible(true);
    } else if (message.media_type?.startsWith('video/')) {
      // Open video player modal
      setVideoPlayerVisible(true);
      setVideoPaused(false);
    } else if (isDocument && mediaState?.localUri) {
      try {
        const supported = await Linking.canOpenURL(mediaState.localUri);
        if (supported) {
          await Linking.openURL(mediaState.localUri);
        } else {
          Alert.alert('Error', 'Cannot open this file type');
        }
      } catch (error) {
        Alert.alert('Error', 'Failed to open document');
      }
    }
  };

  const renderStatusIcon = (colorOverride?: string) => {
  if (!message.isMine && !message.is_mine) return null;

  const iconColor = colorOverride || colors.primary;

  switch (message.status) {
    case 'sending':
      return (
        <View style={styles.statusContainer}>
          <Icon name="checkbox-blank-circle-outline" size={16} color={iconColor} />
        </View>
      );
    case 'sent':
      return (
        <View style={styles.statusContainer}>
          <Icon name="check-circle-outline" size={16} color={iconColor} />
        </View>
      );
    case 'delivered':
      return (
        <View style={styles.statusContainer}>
          <Icon name="check-circle" size={16} color={colorOverride || iconColor} />
        </View>
      );
    case 'read':
      return (
        <View style={styles.statusContainer}>
          <Icon name="check-decagram" size={16} color={colorOverride || colors.success} />
        </View>
      );
    default:
      return null;
  }
};


  const renderImageContent = () => {
    const hasCaption = !!message.message?.trim();
    const removeImageRadius = hasCaption && !isLastInGroup;

    // If this is a sent message and has a local file path, show it immediately
    if (isMine && message.media_uri) {
      return (
        <View >
          {/* Show text/caption if present */}

          <View style={{ position: 'relative' }}>
            <TouchableOpacity
              style={[
                styles.mediaContainer,
                isMine ? styles.sentMediaContainer : styles.receivedMediaContainer
              ]}
              onPress={handleMediaPress}
              activeOpacity={0.8}
            >
              {/* your existing Image or error/loading code goes here */}
              {imageLoading && (
                <View style={[styles.loadingOverlay, { backgroundColor: colors.overlay }]}>
                  <Icon name="hourglass-empty" size={24} color={colors.white} />
                </View>
              )}
              {imageError ? (
                <View style={[styles.errorContainer, { backgroundColor: colors.gray100 }]}>
                  <Icon name="broken-image" size={32} color={colors.danger} />
                  <Text style={[styles.errorText, { color: colors.danger }]}>Failed to load image</Text>
                </View>
              ) : (
                <Image
                  source={{ uri: isMine ? message.media_uri : mediaState?.localUri }}
                  style={[
                    styles.image,
                    isLastInGroup && (isMine ? styles.lastMine : styles.lastPeer),
                    hasCaption && isMine && styles.removeBottomLeftRadius,
                    hasCaption && !isMine && styles.removeBottomRightRadius,
                    removeImageRadius && styles.removeImageBottomRadius,
                  ]}
                  resizeMode="cover"
                  onLoadStart={() => setImageLoading(true)}
                  onLoadEnd={() => setImageLoading(false)}
                  onError={() => {
                    setImageError(true);
                    setImageLoading(false);
                  }}
                />
              )}
            </TouchableOpacity>

            {/* ✅ THIS is the new addition for both mine and peer */}
            {!hasCaption && (
              <View style={styles.imageMessageInfoOverlay}>
                <Text
                  style={[
                    styles.messageTimeNoCaption
                  ]}
                >
                  {formatTime(message.timestamp)}
                </Text>
                {renderStatusIcon()}
              </View>
            )}
          </View>

          {message.message ? (
            <View style={[isMine ? styles.captionTextSent : styles.captionTextReceived,
            message.message?.trim() && !isLastInGroup && styles.removeCaptionRadius
            ]}>
              <Text style={[isMine ? styles.sentText : styles.receivedText]}>{message.message}</Text>
              <View style={styles.messageInfo}>
                <Text style={[
                  styles.messageTime,
                  isMine ? styles.sentTime : styles.receivedTime
                ]}>
                  {formatTime(message.timestamp)}
                </Text>
                {renderStatusIcon(colors.primaryLight3)}
              </View>
            </View>
          ) : null}
        </View>
      );
    }

    // For received media, use Redux download state
    if (mediaState?.status === 'downloading') {
      return (
        <View style={[styles.mediaContainer, styles.placeholderContainer, isMine ? styles.sentMediaContainer : styles.receivedMediaContainer]}>
          <Icon name="cloud-download" size={32} color={colors.primary} />
          <Text style={{ color: colors.textSecondary, marginTop: 8 }}>Downloading...</Text>
        </View>
      );
    }
    if (mediaState?.status === 'failure') {
      return (
        <View style={[styles.mediaContainer, styles.placeholderContainer, isMine ? styles.sentMediaContainer : styles.receivedMediaContainer]}>
          <Icon name="insert-drive-file" size={40} color={colors.primary} />
          <Text style={{ color: colors.danger, marginTop: 8 }}>Download failed</Text>
          {message.media_id && (
            <TouchableOpacity
              style={{ marginTop: 8, alignItems: 'center' }}
              onPress={() => dispatch(mediaDownloadRequested({ mediaId: message.media_id! }))}
              activeOpacity={0.7}
            >
              <Icon name="refresh" size={24} color={colors.primary} />
              <Text style={{ color: colors.primary, marginTop: 2 }}>Retry Download</Text>
            </TouchableOpacity>
          )}
        </View>
      );
    }
    if (mediaState?.status === 'success' && mediaState.localUri) {
      return (
        <View>
          <View style={{ position: 'relative' }}>
            <TouchableOpacity
              style={[
                styles.mediaContainer,
                isMine ? styles.sentMediaContainer : styles.receivedMediaContainer,
              ]}
              onPress={handleMediaPress}
              activeOpacity={0.8}
            >
              {imageLoading && (
                <View style={[styles.loadingOverlay, { backgroundColor: colors.overlay }]}>
                  <Icon name="hourglass-empty" size={24} color={colors.white} />
                </View>
              )}
              {imageError ? (
                <View style={[styles.errorContainer, { backgroundColor: colors.gray100 }]}>
                  <Icon name="broken-image" size={32} color={colors.danger} />
                  <Text style={[styles.errorText, { color: colors.danger }]}>Failed to load image</Text>
                </View>
              ) : (
                <Image
                  source={{ uri: mediaState.localUri }}
                  style={[
                    styles.image,
                    isLastInGroup && (isMine ? styles.lastMine : styles.lastPeer),
                    hasCaption && isMine && styles.removeBottomLeftRadius,
                    hasCaption && !isMine && styles.removeBottomRightRadius,
                    removeImageRadius && styles.removeImageBottomRadius,
                  ]}
                  resizeMode="cover"
                  onLoadStart={() => setImageLoading(true)}
                  onLoadEnd={() => setImageLoading(false)}
                  onError={() => {
                    setImageError(true);
                    setImageLoading(false);
                  }}
                />
              )}
            </TouchableOpacity>

            {/* ✅ Add this overlay for peer images when no caption */}
            {!hasCaption && (
              <View style={styles.imageMessageInfoOverlay}>
                <Text
                  style={[
                    styles.messageTimeNoCaption,
                  ]}
                >
                  {formatTime(message.timestamp)}
                </Text>
                {renderStatusIcon()}
              </View>
            )}
          </View>

          {message.message ? (
            <View
              style={[
                isMine ? styles.captionTextSent : styles.captionTextReceived,
                message.message?.trim() && !isLastInGroup && styles.removeCaptionRadius,
              ]}
            >
              <Text style={[isMine ? styles.sentText : styles.receivedText]}>
                {message.message}
              </Text>
              <View style={styles.messageInfo}>
                <Text
                  style={[
                    styles.messageTime,
                    isMine ? styles.sentTime : styles.receivedTime,
                  ]}
                >
                  {formatTime(message.timestamp)}
                </Text>
                {renderStatusIcon()}
              </View>
            </View>
          ) : null}
        </View>
      );
    }

    // Default: show generic file icon and download button
    return (
      <View style={[styles.mediaContainer, styles.placeholderContainer, isMine ? styles.sentMediaContainer : styles.receivedMediaContainer]}>
        <Icon name="insert-drive-file" size={40} color={colors.primary} />
        <Text style={{ color: colors.textSecondary, marginTop: 8 }}>Media not downloaded</Text>
        {message.media_id && (
          <TouchableOpacity
            style={{ marginTop: 8, alignItems: 'center' }}
            onPress={() => dispatch(mediaDownloadRequested({ mediaId: message.media_id! }))}
            activeOpacity={0.7}
          >
            <Icon name="cloud-download" size={24} color={colors.primary} />
            <Text style={{ color: colors.primary, marginTop: 2 }}>Download</Text>
          </TouchableOpacity>
        )}
      </View>
    );
  };

  const renderVideoContent = () => {
    const hasCaption = !!message.message?.trim();

    return (
      <View>
        <View style={{ position: 'relative' }}>
          <TouchableOpacity
            style={[
              styles.mediaContainer,
              styles.videoContainer,
              isMine ? styles.sentMediaContainer : styles.receivedMediaContainer
            ]}
            onPress={handleMediaPress}
            activeOpacity={0.8}
          >
            <View style={[styles.videoPlaceholder, { backgroundColor: colors.gray100 }]}>
              <Icon name="play-circle" size={48} color={colors.primary} />
              <Text style={[styles.videoText, { color: colors.text }]}>
                {message.media_name || 'Video'}
              </Text>
              {message.media_size && (
                <Text style={[styles.fileSizeText, { color: colors.textSecondary }]}>
                  {formatFileSize(message.media_size)}
                </Text>
              )}
            </View>
          </TouchableOpacity>

          {!hasCaption && (
            <View style={styles.imageMessageInfoOverlay}>
              <Text style={styles.messageTimeNoCaption}>
                {formatTime(message.timestamp)}
              </Text>
              {renderStatusIcon()}
            </View>
          )}
        </View>

        {message.message ? (
          <View style={[
            isMine ? styles.captionTextSent : styles.captionTextReceived,
            message.message?.trim() && !isLastInGroup && styles.removeCaptionRadius
          ]}>
            <Text style={[isMine ? styles.sentText : styles.receivedText]}>
              {message.message}
            </Text>
            <View style={styles.messageInfo}>
              <Text style={[
                styles.messageTime,
                isMine ? styles.sentTime : styles.receivedTime
              ]}>
                {formatTime(message.timestamp)}
              </Text>
              {renderStatusIcon(colors.primaryLight3)}
            </View>
          </View>
        ) : null}
      </View>
    );
  };

  const renderAudioContent = () => {
    const hasCaption = !!message.message?.trim();
    const audioUri = (isMine && message.media_uri) ? message.media_uri : mediaState?.localUri;

    return (
      <View>
        <View style={{ position: 'relative' }}>
          {audioUri ? (
            <VoiceMessagePlayer
              audioUri={audioUri}
              duration={0} // We'll calculate duration from the audio file
              isMine={!!isMine}
              onPress={handleMediaPress}
            />
          ) : (
            <TouchableOpacity
              style={[
                styles.mediaContainer,
                styles.audioContainer,
                isMine ? styles.sentMediaContainer : styles.receivedMediaContainer
              ]}
              onPress={handleMediaPress}
              activeOpacity={0.8}
            >
              <View style={[styles.audioPlaceholder, { backgroundColor: colors.gray100 }]}>
                <Icon name="music-note" size={32} color={colors.primary} />
                <Text style={[styles.audioText, { color: colors.text }]}>
                  {message.media_name || 'Audio'}
                </Text>
                {message.media_size && (
                  <Text style={[styles.fileSizeText, { color: colors.textSecondary }]}>
                    {formatFileSize(message.media_size)}
                  </Text>
                )}
              </View>
            </TouchableOpacity>
          )}

          {!hasCaption && (
            <View style={styles.imageMessageInfoOverlay}>
              <Text style={styles.messageTimeNoCaption}>
                {formatTime(message.timestamp)}
              </Text>
              {renderStatusIcon()}
            </View>
          )}
        </View>

        {message.message ? (
          <View style={[
            isMine ? styles.captionTextSent : styles.captionTextReceived,
            message.message?.trim() && !isLastInGroup && styles.removeCaptionRadius
          ]}>
            <Text style={[isMine ? styles.sentText : styles.receivedText]}>
              {message.message}
            </Text>
            <View style={styles.messageInfo}>
              <Text style={[
                styles.messageTime,
                isMine ? styles.sentTime : styles.receivedTime
              ]}>
                {formatTime(message.timestamp)}
              </Text>
              {renderStatusIcon(colors.primaryLight3)}
            </View>
          </View>
        ) : null}
      </View>
    );
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const styles = createStyles(colors);
  return (
    <View style={[
      styles.messageContainer,
      isMine ? styles.sentMessage : styles.receivedMessage,
      isLastInGroup && styles.lastInGroup
    ]}>
      <View style={[
        styles.messageContent,
        isMine ? styles.sentContent : styles.receivedContent,
        message.status === 'sending' && styles.sendingGradient,
      ]}>
        {message.media_id ? (
          message.media_type?.startsWith('video/') ? renderVideoContent() :
          message.media_type?.startsWith('audio/') ? renderAudioContent() :
          renderImageContent()
        ) : null}

        {/* Image Viewer Modal */}
        {isImage && (
          <ImageViewing
            images={[{ uri: (isMine && message.media_uri) ? message.media_uri : mediaState?.localUri }]}
            imageIndex={0}
            visible={isViewerVisible}
            onRequestClose={() => setViewerVisible(false)}
          />
        )}

        {/* Video Player Modal */}
        {message.media_type?.startsWith('video/') && (
          <Modal
            visible={isVideoPlayerVisible}
            animationType="slide"
            presentationStyle="fullScreen"
            onRequestClose={() => {
              setVideoPlayerVisible(false);
              setVideoPaused(true);
            }}
          >
            <View style={styles.videoPlayerContainer}>
              {/* Header with close button */}
              <View style={[styles.videoPlayerHeader, { backgroundColor: colors.background }]}>
                <TouchableOpacity
                  style={styles.closeButton}
                  onPress={() => {
                    setVideoPlayerVisible(false);
                    setVideoPaused(true);
                  }}
                >
                  <Icon name="close" size={24} color={colors.text} />
                </TouchableOpacity>
                <Text style={[styles.videoPlayerTitle, { color: colors.text }]}>
                  {message.media_name || 'Video'}
                </Text>
              </View>

              {/* Video Player */}
              <View style={styles.videoPlayerContent}>
                <Video
                  source={{
                    uri: (isMine && message.media_uri) ? message.media_uri : mediaState?.localUri
                  }}
                  style={styles.videoPlayer}
                  controls={true}
                  paused={videoPaused}
                  resizeMode="contain"
                  onLoad={() => console.log('Video loaded')}
                  onError={(error) => {
                    console.error('Video error:', error);
                    Alert.alert('Error', 'Failed to load video');
                  }}
                />
              </View>

              {/* Play/Pause Controls */}
              <View style={[styles.videoControls, { backgroundColor: colors.background }]}>
                <TouchableOpacity
                  style={[styles.playPauseButton, { backgroundColor: colors.primary }]}
                  onPress={() => setVideoPaused(!videoPaused)}
                >
                  <Icon
                    name={videoPaused ? "play" : "pause"}
                    size={24}
                    color={colors.white}
                  />
                </TouchableOpacity>
              </View>
            </View>
          </Modal>
        )}
      </View>
    </View>
  );
};

const createStyles = (colors: any) => StyleSheet.create({
  messageContainer: {
    marginVertical: 1,
    marginHorizontal: spacing.md,
  },
  sentMessage: {
    alignItems: 'flex-end',
  },
  receivedMessage: {
    alignItems: 'flex-start',
  },
  lastInGroup: {
    marginBottom: spacing.sm,
  },
  messageContent: {
    maxWidth: '80%',
    borderRadius: radius.lg,
  },
  sentContent: {
  },
  receivedContent: {
  },
  sendingGradient: {
    opacity: 0.7,
  },
  messageText: {
    fontSize: 16,
    fontFamily: 'Outfit-Regular',
    marginBottom: 8,
  },
  sentText: {
    fontSize: 14,
    fontFamily: 'Outfit-Regular',
    color: colors.primaryLight3,
    textAlign: 'right',
  },
  receivedText: {
    fontSize: 14,
    fontFamily: 'Outfit-Regular',
    color: colors.toneLight3,
    textAlign: 'left',
  },
  sentTime: {
    color: colors.primaryLight3,
    fontFamily: 'Outfit-Bold',
  },
  receivedTime: {
    color: colors.toneLight3,
    fontFamily: 'Outfit-Bold',
  },
  messageInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    marginTop: 4,
  },
  messageTime: {
    fontSize: 12,
    fontFamily: 'Outfit-Bold',
    marginRight: 4,
  },
  statusContainer: {
    marginLeft: 4,
  },

  // Media styles
  mediaContainer: {
    overflow: 'hidden',
  },
  sentMediaContainer: {
    alignSelf: 'flex-end',
  },
  receivedMediaContainer: {
    alignSelf: 'flex-start',
  },
  image: {
    width: 200,
    height: 200,
    borderRadius: radius.lg,
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: radius.lg,
    zIndex: 1,
  },
  errorContainer: {
    width: 200,
    height: 200,
    borderRadius: radius.lg,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorText: {
    fontSize: 14,
    fontFamily: 'Outfit-Regular',
    marginTop: 8,
    textAlign: 'center',
  },
  downloadButton: {
    position: 'absolute',
    bottom: 10,
    left: 0,
    right: 0,
    alignItems: 'center',
    backgroundColor: colors.white,
    paddingVertical: 8,
    borderRadius: radius.sm,
    borderWidth: 1,
    borderColor: colors.primary,
  },

  // Document styles
  documentContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: radius.lg,
    backgroundColor: colors.cardBackground,
    borderWidth: 1,
    borderColor: colors.border,
    marginTop: 4,
    minHeight: 60,
  },
  sentDocumentContainer: {
    alignSelf: 'flex-end',

  },
  receivedDocumentContainer: {
    alignSelf: 'flex-start',
  },
  documentIcon: {
    width: 40,
    height: 40,
    borderRadius: radius.sm,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  documentInfo: {
    flex: 1,
    marginRight: 8,
  },
  documentName: {
    fontSize: 14,
    fontFamily: 'Outfit-Medium',
    marginBottom: 2,
  },
  documentSize: {
    fontSize: 12,
    fontFamily: 'Outfit-Regular',
  },
  placeholderContainer: {
    backgroundColor: '#e0e0e0', // WhatsApp-like gray
    justifyContent: 'center',
    alignItems: 'center',
    width: 200,
    height: 200,
    borderRadius: radius.lg,
    borderWidth: 1,
    borderColor: '#bdbdbd',
    marginTop: 4,
  },
  downloadButtonCenter: {
    justifyContent: 'center',
    alignItems: 'center',
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#fff',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.15,
    shadowRadius: 2,
  },
  captionTextSent: {
    width: 200,
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderBottomLeftRadius: radius.lg,
  },
  captionTextReceived: {
    width: 200,
    backgroundColor: colors.tone,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderBottomRightRadius: radius.lg,
  },
  lastMine: {
    borderBottomRightRadius: 0,
  },
  lastPeer: {
    borderBottomLeftRadius: 0,
  },
  removeBottomLeftRadius: {
    borderBottomLeftRadius: 0,
  },
  removeBottomRightRadius: {
    borderBottomRightRadius: 0,
  },
  removeCaptionRadius: {
    borderBottomLeftRadius: radius.lg,
    borderBottomRightRadius: radius.lg,
  },
  removeImageBottomRadius: {
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
  },
  singleTick: {
    fontSize: 12,
    fontFamily: 'Outfit-Regular',
  },
  doubleTick: {
    fontSize: 12,
    fontFamily: 'Outfit-Regular',
  },
  imageMessageInfoOverlay: {
    position: 'absolute',
    bottom: 8,
    right: 8,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.4)',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: radius.sm,
  },
  messageTimeNoCaption:{
    color:'#ffffff',
    fontFamily: 'Outfit-Regular',
    fontSize: 12,
  },

  // Video styles
  videoContainer: {
    borderRadius: radius.lg,
    overflow: 'hidden',
  },
  videoPlaceholder: {
    width: 200,
    height: 150,
    borderRadius: radius.lg,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 16,
  },
  videoText: {
    fontSize: 14,
    fontFamily: 'Outfit-Medium',
    marginTop: 8,
    textAlign: 'center',
  },

  // Audio styles
  audioContainer: {
    borderRadius: radius.lg,
    overflow: 'hidden',
  },
  audioPlaceholder: {
    width: 200,
    height: 80,
    borderRadius: radius.lg,
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  audioText: {
    fontSize: 14,
    fontFamily: 'Outfit-Medium',
    marginLeft: 12,
    flex: 1,
  },

  // Common file styles
  fileSizeText: {
    fontSize: 12,
    fontFamily: 'Outfit-Regular',
    marginTop: 4,
  },

  // Video Player Modal styles
  videoPlayerContainer: {
    flex: 1,
    backgroundColor: 'black',
  },
  videoPlayerHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.md,
    paddingTop: spacing.xl + 20, // Account for status bar
  },
  closeButton: {
    padding: spacing.sm,
    marginRight: spacing.md,
  },
  videoPlayerTitle: {
    fontSize: 16,
    fontFamily: 'Outfit-Medium',
    flex: 1,
  },
  videoPlayerContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  videoPlayer: {
    width: '100%',
    height: '100%',
  },
  videoControls: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: spacing.lg,
    paddingHorizontal: spacing.md,
  },
  playPauseButton: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
  },

});

export default MediaMessageItem;