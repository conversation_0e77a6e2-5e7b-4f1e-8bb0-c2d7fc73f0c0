.emoji-bar {
  display: flex;
  justify-content: space-around;
  align-items: center;
  background-color: var(--card-background);
  margin-bottom: 8px;
  /* Prevent text selection in the entire bar */
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}
.emoji-bar {
  touch-action: none; /* disables default touch behavior like scrolling */
  -webkit-user-select: none; /* prevents selection in iOS Safari */
  user-select: none; /* prevents text selection on long-press */
}


.emoji-button {
  background: none;
  border: none;
  font-size: 20px;
  color: #666;
  cursor: pointer;
  padding: 30px;
  border-radius: 50%;
  transition: transform 0.2s ease; /* Only transition the transform property */
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  -webkit-touch-callout: none; /* iOS Safari */
  -webkit-user-select: none; /* Safari */
  -khtml-user-select: none; /* Konqueror HTML */
  -moz-user-select: none; /* Firefox */
  -ms-user-select: none; /* Internet Explorer/Edge */
  user-select: none; /* Non-prefixed version, currently supported by Chrome and Opera */
  touch-action: manipulation; /* Prevents default touch actions like double-tap zoom */
}

/* Remove hover effect on mobile to prevent unwanted highlighting */
@media (hover: hover) {
  .emoji-button:hover {
    color: var(--primary-color);
    transform: scale(1.05);
  }
}

/* Remove active state scaling to prevent unwanted effects on single tap */
.emoji-button:active {
  transform: none;
  background: none; /* Ensure no background on tap */
}

/* Only apply long-press active styling */
.emoji-button.long-press-active {
  background-color: rgba(240, 79, 61, 0.2);
  color: var(--primary-color);
  transform: scale(1.3);
  box-shadow: 0 0 8px rgba(240, 79, 61, 0.3);
}

/* Ensure no background remains after release */
.emoji-button:not(.long-press-active) {
  background-color: transparent !important;
  box-shadow: none !important;
}

/* Mobile-specific styles */
@media (max-width: 767px) {
  .emoji-button {
    width: 44px;
    height: 44px;
    font-size: 22px;
  }
}

/* Desktop styles */
@media (min-width: 768px) {
  .emoji-bar {
    padding: 8px 16px;
  }

  .emoji-button {
    font-size: 22px;
  }
}
